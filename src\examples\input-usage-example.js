/**
 * 输入系统使用示例
 * 展示如何在Cantos MMORPG项目中使用新的输入管理器
 */

import {
    InputManager,
    getGlobalInputManager,
    initializeGlobalInputManager
} from '../core/InputManager.js';
import { InputEventTypes } from '../core/InputEventTypes.js';
import {
    InputConfig,
    getGlobalInputConfig,
    initializeGlobalInputConfig
} from '../core/InputConfig.js';
import {
    InputEventSystem,
    inputSystem,
    createPlayerInputEntity
} from '../core/InputSystem.js';
import { ecsWorld } from '../core/ecs/bitECSSystem.js';

/**
 * 初始化输入系统示例
 * @param {Scene} scene - Babylon.js场景
 * @returns {Object} 示例对象
 */
export async function initializeInputExample(scene) {
    console.log('🎮 开始输入系统使用示例...');

    const example = {
        inputManager: null,
        inputConfig: null,
        inputEventSystem: null,
        playerEntity: null,
        stats: {
            keyPresses: 0,
            mouseClicks: 0,
            movementEvents: 0
        }
    };

    try {
        // 1. 初始化输入配置
        console.log('📋 初始化输入配置...');
        example.inputConfig = initializeGlobalInputConfig({
            sensitivity: {
                mouseSensitivity: 1.5,
                zoomSensitivity: 1.2
            },
            advanced: {
                debugMode: true,
                logInputEvents: true
            }
        });

        // 2. 初始化输入管理器
        console.log('🎮 初始化输入管理器...');
        example.inputManager = await initializeGlobalInputManager(scene, {
            debug: true,
            mouseSensitivity: 1.5,
            zoomSensitivity: 1.2
        });

        // 3. 创建输入事件系统
        console.log('⚡ 创建输入事件系统...');
        example.inputEventSystem = new InputEventSystem(ecsWorld);
        example.inputEventSystem.initialize(example.inputManager);

        // 4. 创建玩家输入实体
        console.log('👤 创建玩家输入实体...');
        example.playerEntity = createPlayerInputEntity(ecsWorld, 0, 0, 0);

        // 5. 设置示例事件监听器
        setupExampleEventListeners(example);

        // 6. 设置自定义输入映射
        setupCustomInputMappings(example);

        // 7. 启动ECS系统
        ecsWorld.registerSystem(inputSystem, 'InputSystem');
        ecsWorld.start();

        console.log('✅ 输入系统示例初始化完成');

        // 显示使用说明
        showUsageInstructions();

        return example;

    } catch (error) {
        console.error('❌ 输入系统示例初始化失败:', error);
        return null;
    }
}

/**
 * 设置示例事件监听器
 * @param {Object} example - 示例对象
 */
function setupExampleEventListeners(example) {
    const { inputManager, stats } = example;

    // 监听所有移动事件
    inputManager.on(InputEventTypes.MOVEMENT_START, (data) => {
        console.log('🚶 开始移动:', data.movement);
        stats.movementEvents++;
    });

    inputManager.on(InputEventTypes.MOVEMENT_UPDATE, (data) => {
        console.log('🏃 移动更新:', data.movement);
        stats.movementEvents++;
    });

    inputManager.on(InputEventTypes.MOVEMENT_STOP, (data) => {
        console.log('🛑 停止移动');
        stats.movementEvents++;
    });

    // 监听动作事件
    inputManager.on(InputEventTypes.ACTION_JUMP, (data) => {
        console.log('🦘 跳跃!', data);
        stats.keyPresses++;
    });

    inputManager.on(InputEventTypes.ACTION_ATTACK, (data) => {
        console.log('⚔️ 攻击!', data);
        if (data.device === 'mouse') {
            stats.mouseClicks++;
        } else {
            stats.keyPresses++;
        }
    });

    inputManager.on(InputEventTypes.ACTION_INTERACT, (data) => {
        console.log('🤝 交互!', data);
        stats.keyPresses++;
    });

    // 监听UI事件
    inputManager.on(InputEventTypes.UI_MENU, (data) => {
        console.log('📋 菜单切换!', data);
        stats.keyPresses++;
    });

    inputManager.on(InputEventTypes.UI_INVENTORY, (data) => {
        console.log('🎒 背包切换!', data);
        stats.keyPresses++;
    });

    // 监听技能事件
    for (let i = 0; i <= 9; i++) {
        const eventType = i === 0 ? InputEventTypes.SKILL_SLOT_0 : InputEventTypes[`SKILL_SLOT_${i}`];
        inputManager.on(eventType, (data) => {
            console.log(`🔮 使用技能槽 ${i}!`, data);
            stats.keyPresses++;
        });
    }

    // 监听相机事件
    inputManager.on(InputEventTypes.CAMERA_ROTATE, (data) => {
        if (data.rotation.lengthSquared() > 1) { // 只记录较大的移动
            console.log('📷 相机旋转:', data.rotation);
        }
    });

    inputManager.on(InputEventTypes.CAMERA_ZOOM, (data) => {
        console.log('🔍 相机缩放:', data.zoom);
    });

    console.log('👂 示例事件监听器已设置');
}

/**
 * 设置自定义输入映射
 * @param {Object} example - 示例对象
 */
function setupCustomInputMappings(example) {
    const { inputManager, inputConfig } = example;

    // 添加自定义键位映射
    inputManager.setInputMapping('t', InputEventTypes.UI_CHAT, { custom: true });
    inputManager.setInputMapping('g', InputEventTypes.ACTION_INTERACT, { custom: true });
    inputManager.setInputMapping('mouse:1', InputEventTypes.CAMERA_ROTATE, { custom: true }); // 中键旋转

    // 保存自定义配置
    inputConfig.setKeyboardMapping('t', InputEventTypes.UI_CHAT, { custom: true });
    inputConfig.setKeyboardMapping('g', InputEventTypes.ACTION_INTERACT, { custom: true });
    inputConfig.setMouseMapping('mouse:1', InputEventTypes.CAMERA_ROTATE, { custom: true });

    // 监听自定义事件
    inputManager.on(InputEventTypes.UI_CHAT, (data) => {
        if (data.custom) {
            console.log('💬 自定义聊天键 (T) 被按下!', data);
        }
    });

    console.log('⚙️ 自定义输入映射已设置');
}

/**
 * 显示使用说明
 */
function showUsageInstructions() {
    console.log(`
🎮 输入系统使用说明:

📋 基本控制:
  WASD / 方向键 - 移动
  空格键 - 跳跃
  Shift - 奔跑
  E - 交互
  F - 攻击

🖱️ 鼠标控制:
  左键 - 攻击
  右键 - 交互
  滚轮 - 缩放
  移动 - 旋转视角

🎯 UI控制:
  ESC - 菜单
  I - 背包
  M - 地图
  Enter - 聊天
  T - 聊天 (自定义)

🔮 技能快捷键:
  1-9, 0 - 技能槽

🔧 调试命令:
  window.inputManager - 访问输入管理器
  window.inputConfig - 访问输入配置
  inputManager.getStats() - 获取统计信息
  inputConfig.exportToJSON() - 导出配置
    `);
}

/**
 * 获取示例统计信息
 * @param {Object} example - 示例对象
 * @returns {Object} 统计信息
 */
export function getExampleStats(example) {
    if (!example) return null;

    const inputStats = example.inputManager ? example.inputManager.getStats() : {};
    const configStats = example.inputConfig ? example.inputConfig.getStats() : {};

    return {
        ...example.stats,
        inputManager: inputStats,
        inputConfig: configStats,
        timestamp: new Date().toISOString()
    };
}

/**
 * 清理示例资源
 * @param {Object} example - 示例对象
 */
export function cleanupInputExample(example) {
    if (!example) return;

    console.log('🧹 清理输入系统示例...');

    // 停止ECS系统
    if (ecsWorld) {
        ecsWorld.stop();
    }

    // 销毁输入事件系统
    if (example.inputEventSystem) {
        example.inputEventSystem.destroy();
    }

    // 销毁输入管理器
    if (example.inputManager) {
        example.inputManager.destroy();
    }

    console.log('✅ 输入系统示例清理完成');
}

/**
 * 演示配置管理功能
 * @param {Object} example - 示例对象
 */
export function demonstrateConfigManagement(example) {
    if (!example || !example.inputConfig) return;

    console.log('⚙️ 演示配置管理功能...');

    const config = example.inputConfig;

    // 1. 修改敏感度
    console.log('📊 修改敏感度设置...');
    config.setSensitivity('mouseSensitivity', 2.0);
    config.setSensitivity('zoomSensitivity', 0.5);

    // 2. 添加自定义映射
    console.log('🔧 添加自定义映射...');
    config.setKeyboardMapping('h', InputEventTypes.UI_MENU, { help: true });
    config.setMouseMapping('mouse:3', InputEventTypes.ACTION_ATTACK, { special: true });

    // 3. 导出配置
    console.log('📤 导出配置...');
    const exportedConfig = config.exportToJSON();
    console.log('导出的配置:', exportedConfig);

    // 4. 验证配置
    console.log('✅ 验证配置...');
    const validation = config.validateConfig();
    console.log('验证结果:', validation);

    // 5. 保存到本地存储
    console.log('💾 保存到本地存储...');
    const saved = config.saveToStorage();
    console.log('保存结果:', saved);

    // 6. 获取统计信息
    console.log('📈 获取统计信息...');
    const stats = config.getStats();
    console.log('配置统计:', stats);
}

console.log('🎮 输入系统使用示例模块已加载');
