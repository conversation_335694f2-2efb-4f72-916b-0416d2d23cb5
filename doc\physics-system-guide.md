# 物理系统集成指南

## 概述

Cantos MMORPG项目的物理系统基于Havok物理引擎，提供了完整的物理模拟功能，包括刚体动力学、碰撞检测、触发器和射线检测等。物理系统与bitECS架构深度集成，为游戏提供高性能的物理计算能力。

## 核心组件

### 1. PhysicsManager (物理管理器)

PhysicsManager是物理系统的核心类，负责管理Havok物理引擎的生命周期和所有物理相关操作。

#### 主要功能
- Havok物理引擎初始化和配置
- 刚体创建和管理
- 触发器创建和事件处理
- 射线检测
- 物理材质管理
- 性能统计和调试

#### 基本使用

```javascript
import { PhysicsManager } from './core/physics/index.js';

// 创建物理管理器
const physicsManager = new PhysicsManager(scene);

// 初始化物理引擎
await physicsManager.initialize({
    gravity: new Vector3(0, -9.81, 0),
    enableDebugDraw: false
});

// 检查是否准备就绪
if (physicsManager.isReady()) {
    console.log('物理引擎已准备就绪');
}
```

### 2. PhysicsUtils (物理工具类)

PhysicsUtils提供了便捷的工具函数，简化物理对象的创建和管理。

#### 预设配置

```javascript
import { PhysicsUtils, PhysicsBodyPresets } from './core/physics/index.js';

// 使用预设创建玩家角色
const player = PhysicsUtils.createPhysicsEntity(
    ecsWorld,
    physicsManager,
    scene,
    {
        preset: 'PLAYER',
        position: new Vector3(0, 5, 0)
    }
);

// 创建地面
const ground = PhysicsUtils.createPhysicsGround(
    ecsWorld,
    physicsManager,
    scene,
    {
        width: 50,
        height: 50
    }
);
```

### 3. ECS集成

物理系统与bitECS深度集成，提供了专门的组件和系统。

#### Physics组件

```javascript
import { Physics } from './core/ecs/bitECSSystem.js';

// Physics组件包含以下属性：
// - mass: 质量
// - velocityX/Y/Z: 速度
// - friction: 摩擦力
// - restitution: 弹性系数
// - isKinematic: 是否为运动学物体
// - isStatic: 是否为静态物体
// - physicsBodyId: 关联的物理体ID
```

#### 物理系统

```javascript
import { physicsSystem, havokPhysicsSyncSystem } from './core/physics/index.js';

// 注册物理系统到ECS世界
ecsWorld.registerSystem(physicsSystem, 'PhysicsSystem');
ecsWorld.registerSystem((world, deltaTime) => {
    havokPhysicsSyncSystem(world, deltaTime, physicsManager);
}, 'HavokPhysicsSyncSystem');
```

## 物理材质预设

系统提供了多种预定义的物理材质：

```javascript
import { PhysicsMaterialPresets } from './core/physics/index.js';

// 可用材质预设：
// - DEFAULT: 默认材质
// - ICE: 冰面材质（低摩擦）
// - RUBBER: 橡胶材质（高弹性）
// - METAL: 金属材质
// - WOOD: 木材材质
// - STONE: 石头材质
// - PLAYER: 玩家角色材质
```

## 物理形状类型

支持多种物理形状：

```javascript
import { PhysicsShapeTypes } from './core/physics/index.js';

// 支持的形状类型：
// - BOX: 盒子形状
// - SPHERE: 球体形状
// - CAPSULE: 胶囊形状
// - CONVEX_HULL: 凸包形状
// - MESH: 网格形状
```

## 高级功能

### 1. 射线检测

```javascript
// 执行射线检测
const origin = new Vector3(0, 10, 0);
const direction = new Vector3(0, -1, 0);
const distance = 20;

const result = physicsManager.raycast(origin, direction, distance, {
    ignoreList: [playerBody],  // 忽略列表
    includeList: null          // 包含列表
});

if (result && result.hasHit) {
    console.log('射线命中:', result.point);
}
```

### 2. 触发器

```javascript
// 创建触发器
const triggerCallback = (collisionEvent, triggerBody) => {
    console.log('触发器被触发!', collisionEvent);
};

const trigger = physicsManager.createTrigger(mesh, triggerCallback, {
    shapeType: PhysicsShapeTypes.BOX,
    shapeOptions: {
        size: new Vector3(2, 2, 2)
    }
});
```

### 3. 力和冲量

```javascript
// 应用力
const force = new Vector3(100, 0, 0);
physicsManager.applyForce(physicsBody, force);

// 应用冲量
const impulse = new Vector3(50, 100, 0);
physicsManager.applyImpulse(physicsBody, impulse);
```

### 4. 速度控制

```javascript
// 获取速度
const velocity = physicsManager.getVelocity(physicsBody);

// 设置速度
const newVelocity = new Vector3(5, 0, 0);
physicsManager.setVelocity(physicsBody, newVelocity);
```

## 性能优化

### 1. 物理体管理

```javascript
// 获取性能统计
const stats = physicsManager.getStats();
console.log('刚体数量:', stats.rigidBodiesCount);
console.log('触发器数量:', stats.triggersCount);
console.log('每帧射线检测次数:', stats.raycastsPerFrame);

// 重置统计
physicsManager.resetStats();
```

### 2. 调试功能

```javascript
// 启用调试绘制
physicsManager.setDebugDraw(true);

// 暂停/恢复物理模拟
physicsManager.pause();
physicsManager.resume();
```

### 3. 资源清理

```javascript
// 移除物理体
physicsManager.removeRigidBody(physicsBody);

// 移除触发器
physicsManager.removeTrigger(triggerBody);

// 清理所有资源
physicsManager.dispose();
```

## 与SceneManager集成

物理系统已完全集成到SceneManager中：

```javascript
// 获取当前场景的物理管理器
const physicsManager = sceneManager.getCurrentPhysicsManager();

// 获取指定场景的物理管理器
const specificPhysicsManager = sceneManager.getPhysicsManager('scene-id');
```

场景管理器会自动：
- 为每个场景创建独立的物理管理器
- 在场景切换时管理物理资源
- 在场景销毁时清理物理资源
- 在每帧更新中调用物理系统更新

## 最佳实践

### 1. 物理体创建

```javascript
// 推荐：使用PhysicsUtils创建物理对象
const entity = PhysicsUtils.createPhysicsEntity(ecsWorld, physicsManager, scene, {
    preset: 'BOX',
    position: new Vector3(0, 5, 0),
    customPhysicsOptions: {
        mass: 10,
        friction: 0.8
    }
});

// 避免：直接创建复杂的物理体配置
```

### 2. 性能考虑

- 使用简化的碰撞形状（盒子、球体）而不是复杂的网格形状
- 合理设置物理体的质量和材质属性
- 定期清理不需要的物理体
- 使用触发器而不是持续的碰撞检测来处理区域事件

### 3. 错误处理

```javascript
try {
    const physicsBody = physicsManager.createRigidBody(mesh, options);
    if (!physicsBody) {
        console.warn('物理体创建失败');
        // 处理失败情况
    }
} catch (error) {
    console.error('物理操作失败:', error);
    // 错误恢复逻辑
}
```

## 故障排除

### 常见问题

1. **物理引擎初始化失败**
   - 检查Havok库是否正确加载
   - 确认场景对象有效
   - 查看浏览器控制台错误信息

2. **物理体不响应**
   - 确认物理体质量大于0（动态物体）
   - 检查物理体是否被设置为静态
   - 验证物理引擎是否正在运行

3. **性能问题**
   - 减少复杂物理形状的使用
   - 优化射线检测频率
   - 使用对象池管理物理体

### 调试技巧

```javascript
// 启用详细日志
physicsManager.setDebugDraw(true);

// 监控性能
setInterval(() => {
    const stats = physicsManager.getStats();
    console.log('物理系统状态:', stats);
}, 1000);

// 检查物理体状态
console.log('物理体速度:', physicsManager.getVelocity(physicsBody));
```

## 示例代码

完整的使用示例请参考 `src/examples/physics-usage-example.js` 文件，其中包含了：
- 地面创建
- 物理球体和箱子
- 触发器区域
- 射线检测演示
- 力的应用示例

运行游戏后，可以通过浏览器控制台使用 `window.physicsExample` 对象来交互式地测试物理功能。
