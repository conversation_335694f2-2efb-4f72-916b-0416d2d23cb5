/**
 * 物理系统模块导出
 * 为Cantos MMORPG项目提供物理系统相关的所有导出
 */

// 导出主要的物理管理器类
export {
    PhysicsManager,
    PhysicsMaterialPresets,
    PhysicsShapeTypes
} from './PhysicsManager.js';

// 导出物理工具类
export {
    PhysicsUtils,
    PhysicsBodyPresets
} from './PhysicsUtils.js';

// 导出物理系统相关的ECS系统
export {
    physicsSystem,
    havokPhysicsSyncSystem,
    createPhysicsBodyForEntity,
    applyForceToEntity,
    applyImpulseToEntity
} from '../ecs/systems/PhysicsSystem.js';

console.log('物理系统模块索引已加载');
