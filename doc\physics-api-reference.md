# 物理系统 API 参考

## PhysicsManager

### 构造函数

```javascript
new PhysicsManager(scene)
```

**参数:**
- `scene` (Scene): Babylon.js场景实例

### 方法

#### initialize(options)

初始化物理引擎。

```javascript
await physicsManager.initialize(options)
```

**参数:**
- `options` (Object): 初始化选项
  - `gravity` (Vector3): 重力向量，默认 `(0, -9.81, 0)`
  - `enableDebugDraw` (boolean): 是否启用调试绘制，默认 `false`
  - `substeps` (number): 子步数，默认 `1`
  - `maxSubsteps` (number): 最大子步数，默认 `4`
  - `fixedTimeStep` (number): 固定时间步长，默认 `1/60`

**返回值:** `Promise<boolean>` - 初始化是否成功

#### isReady()

检查物理引擎是否已准备就绪。

```javascript
const ready = physicsManager.isReady()
```

**返回值:** `boolean`

#### setGravity(gravity)

设置重力。

```javascript
physicsManager.setGravity(gravity)
```

**参数:**
- `gravity` (Vector3): 重力向量

#### getGravity()

获取当前重力设置。

```javascript
const gravity = physicsManager.getGravity()
```

**返回值:** `Vector3`

#### createPhysicsShape(shapeType, options)

创建物理形状。

```javascript
const shape = physicsManager.createPhysicsShape(shapeType, options)
```

**参数:**
- `shapeType` (string): 形状类型 (`'box'`, `'sphere'`, `'capsule'`, `'convexHull'`, `'mesh'`)
- `options` (Object): 形状选项
  - `center` (Vector3): 中心点，默认 `Vector3.Zero()`
  - `rotation` (Quaternion): 旋转，默认 `Quaternion.Identity()`
  - `material` (Object): 材质属性
  - 其他特定于形状的选项

**返回值:** `PhysicsShape | null`

#### createRigidBody(mesh, options)

创建刚体。

```javascript
const physicsBody = physicsManager.createRigidBody(mesh, options)
```

**参数:**
- `mesh` (Mesh): 关联的网格对象
- `options` (Object): 刚体选项
  - `motionType` (string): 运动类型 (`'DYNAMIC'`, `'STATIC'`, `'KINEMATIC'`)
  - `mass` (number): 质量，默认 `1`
  - `shapeType` (string): 形状类型
  - `shapeOptions` (Object): 形状选项
  - `material` (Object): 材质属性
  - `startAsleep` (boolean): 是否开始时休眠

**返回值:** `PhysicsBody | null`

#### createTrigger(mesh, callback, options)

创建触发器。

```javascript
const trigger = physicsManager.createTrigger(mesh, callback, options)
```

**参数:**
- `mesh` (Mesh): 关联的网格对象
- `callback` (Function): 触发回调函数
- `options` (Object): 触发器选项

**返回值:** `PhysicsBody | null`

#### raycast(origin, direction, distance, options)

执行射线检测。

```javascript
const result = physicsManager.raycast(origin, direction, distance, options)
```

**参数:**
- `origin` (Vector3): 射线起点
- `direction` (Vector3): 射线方向
- `distance` (number): 射线距离，默认 `100`
- `options` (Object): 射线检测选项
  - `ignoreList` (Array): 忽略的物理体列表
  - `includeList` (Array): 包含的物理体列表

**返回值:** `PhysicsRaycastResult | null`

#### applyForce(physicsBody, force, contactPoint)

对物理体应用力。

```javascript
physicsManager.applyForce(physicsBody, force, contactPoint)
```

**参数:**
- `physicsBody` (PhysicsBody): 物理体
- `force` (Vector3): 力向量
- `contactPoint` (Vector3): 作用点，可选

#### applyImpulse(physicsBody, impulse, contactPoint)

对物理体应用冲量。

```javascript
physicsManager.applyImpulse(physicsBody, impulse, contactPoint)
```

**参数:**
- `physicsBody` (PhysicsBody): 物理体
- `impulse` (Vector3): 冲量向量
- `contactPoint` (Vector3): 作用点，可选

#### getVelocity(physicsBody)

获取物理体速度。

```javascript
const velocity = physicsManager.getVelocity(physicsBody)
```

**参数:**
- `physicsBody` (PhysicsBody): 物理体

**返回值:** `Vector3`

#### setVelocity(physicsBody, velocity)

设置物理体速度。

```javascript
physicsManager.setVelocity(physicsBody, velocity)
```

**参数:**
- `physicsBody` (PhysicsBody): 物理体
- `velocity` (Vector3): 速度向量

#### removeRigidBody(physicsBody)

移除刚体。

```javascript
physicsManager.removeRigidBody(physicsBody)
```

**参数:**
- `physicsBody` (PhysicsBody): 要移除的物理体

#### removeTrigger(triggerBody)

移除触发器。

```javascript
physicsManager.removeTrigger(triggerBody)
```

**参数:**
- `triggerBody` (PhysicsBody): 要移除的触发器

#### getStats()

获取性能统计信息。

```javascript
const stats = physicsManager.getStats()
```

**返回值:** `Object`
- `rigidBodiesCount` (number): 刚体数量
- `triggersCount` (number): 触发器数量
- `raycastsPerFrame` (number): 每帧射线检测次数
- `isInitialized` (boolean): 是否已初始化
- `gravity` (string): 重力设置
- `physicsEngineType` (string): 物理引擎类型

#### resetStats()

重置性能统计。

```javascript
physicsManager.resetStats()
```

#### update(deltaTime)

更新物理系统。

```javascript
physicsManager.update(deltaTime)
```

**参数:**
- `deltaTime` (number): 时间增量

#### setDebugDraw(enable)

启用/禁用调试绘制。

```javascript
physicsManager.setDebugDraw(enable)
```

**参数:**
- `enable` (boolean): 是否启用

#### pause()

暂停物理模拟。

```javascript
physicsManager.pause()
```

#### resume()

恢复物理模拟。

```javascript
physicsManager.resume()
```

#### dispose()

清理所有物理资源。

```javascript
physicsManager.dispose()
```

## PhysicsUtils

### 静态方法

#### createPhysicsEntity(ecsWorld, physicsManager, scene, options)

创建完整的物理实体。

```javascript
const entity = PhysicsUtils.createPhysicsEntity(ecsWorld, physicsManager, scene, options)
```

**参数:**
- `ecsWorld` (Object): ECS世界实例
- `physicsManager` (PhysicsManager): 物理管理器
- `scene` (Scene): Babylon.js场景
- `options` (Object): 创建选项

**返回值:** `Object | null`

#### createPhysicsGround(ecsWorld, physicsManager, scene, options)

创建物理地面。

```javascript
const ground = PhysicsUtils.createPhysicsGround(ecsWorld, physicsManager, scene, options)
```

#### createPhysicsBall(ecsWorld, physicsManager, scene, position, options)

创建物理球体。

```javascript
const ball = PhysicsUtils.createPhysicsBall(ecsWorld, physicsManager, scene, position, options)
```

#### createPhysicsBox(ecsWorld, physicsManager, scene, position, options)

创建物理箱子。

```javascript
const box = PhysicsUtils.createPhysicsBox(ecsWorld, physicsManager, scene, position, options)
```

#### distance(point1, point2)

计算两点间距离。

```javascript
const dist = PhysicsUtils.distance(point1, point2)
```

#### degreesToRadians(degrees)

角度转弧度。

```javascript
const radians = PhysicsUtils.degreesToRadians(degrees)
```

#### radiansToDegrees(radians)

弧度转角度。

```javascript
const degrees = PhysicsUtils.radiansToDegrees(radians)
```

## 常量和预设

### PhysicsMaterialPresets

```javascript
PhysicsMaterialPresets.DEFAULT    // 默认材质
PhysicsMaterialPresets.ICE        // 冰面材质
PhysicsMaterialPresets.RUBBER     // 橡胶材质
PhysicsMaterialPresets.METAL      // 金属材质
PhysicsMaterialPresets.WOOD       // 木材材质
PhysicsMaterialPresets.STONE      // 石头材质
PhysicsMaterialPresets.PLAYER     // 玩家材质
```

### PhysicsShapeTypes

```javascript
PhysicsShapeTypes.BOX         // 'box'
PhysicsShapeTypes.SPHERE      // 'sphere'
PhysicsShapeTypes.CAPSULE     // 'capsule'
PhysicsShapeTypes.CONVEX_HULL // 'convexHull'
PhysicsShapeTypes.MESH        // 'mesh'
```

### PhysicsBodyPresets

```javascript
PhysicsBodyPresets.PLAYER     // 玩家角色预设
PhysicsBodyPresets.BOX        // 箱子预设
PhysicsBodyPresets.BALL       // 球体预设
PhysicsBodyPresets.GROUND     // 地面预设
PhysicsBodyPresets.TRIGGER    // 触发器预设
```

## ECS集成

### Physics组件

```javascript
const Physics = {
    mass: [],           // 质量
    velocityX: [],      // X轴速度
    velocityY: [],      // Y轴速度
    velocityZ: [],      // Z轴速度
    friction: [],       // 摩擦力
    restitution: [],    // 弹性系数
    isKinematic: [],    // 是否为运动学物体
    isStatic: [],       // 是否为静态物体
    physicsBodyId: []   // 关联的物理体ID
}
```

### 系统函数

#### physicsSystem(world, deltaTime)

基础物理系统。

#### havokPhysicsSyncSystem(world, deltaTime, physicsManager)

Havok物理同步系统。

#### createPhysicsBodyForEntity(ecsWorld, physicsManager, entity, mesh, options)

为ECS实体创建物理体。

#### applyForceToEntity(physicsManager, entity, force, contactPoint)

对ECS实体应用力。

#### applyImpulseToEntity(physicsManager, entity, impulse, contactPoint)

对ECS实体应用冲量。
