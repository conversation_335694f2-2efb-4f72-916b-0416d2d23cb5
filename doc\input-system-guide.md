# Cantos MMORPG 输入系统指南

## 概述

Cantos MMORPG的输入系统是一个现代化、模块化的输入处理框架，专为MMORPG游戏设计。它提供了统一的输入管理、可配置的键位映射、ECS集成以及多设备支持。

## 核心特性

### 🎮 多设备支持
- **键盘输入**: 完整的键盘事件处理
- **鼠标输入**: 按钮、移动、滚轮支持
- **触摸输入**: 为移动设备预留接口
- **手柄输入**: 为手柄控制预留接口

### ⚙️ 可配置映射
- **动态键位绑定**: 运行时修改键位映射
- **配置持久化**: 自动保存到本地存储
- **导入导出**: JSON格式配置文件
- **验证机制**: 配置有效性检查

### 🏗️ ECS集成
- **bitECS兼容**: 与项目ECS架构深度集成
- **组件驱动**: PlayerInput组件自动更新
- **系统化处理**: 输入系统统一处理所有输入实体

### 📡 事件驱动
- **事件发射器**: 基于EventEmitter的事件系统
- **类型化事件**: 预定义的事件类型
- **监听器管理**: 灵活的事件监听机制

## 架构组件

### 1. InputManager (核心输入管理器)

负责统一管理所有输入设备和事件处理。

```javascript
import { InputManager, initializeGlobalInputManager } from './core/InputManager.js';

// 初始化输入管理器
const inputManager = await initializeGlobalInputManager(scene, {
    debug: false,
    mouseSensitivity: 1.0,
    zoomSensitivity: 1.0
});

// 监听移动事件
inputManager.on('movement:update', (data) => {
    console.log('移动:', data.movement);
});
```

### 2. InputConfig (配置管理器)

管理输入配置的加载、保存和修改。

```javascript
import { InputConfig, initializeGlobalInputConfig } from './core/InputConfig.js';

// 初始化配置
const config = initializeGlobalInputConfig({
    sensitivity: {
        mouseSensitivity: 2.0
    }
});

// 修改键位映射
config.setKeyboardMapping('t', 'ui:chat');

// 保存配置
config.saveToStorage();
```

### 3. InputSystem (ECS集成)

将输入管理器与bitECS系统集成。

```javascript
import { inputSystem, InputEventSystem } from './core/InputSystem.js';
import { ecsWorld } from './core/ecs/bitECSSystem.js';

// 注册输入系统
ecsWorld.registerSystem(inputSystem, 'InputSystem');

// 创建输入事件系统
const eventSystem = new InputEventSystem(ecsWorld);
eventSystem.initialize(inputManager);
```

## 事件类型

### 移动事件
- `movement:start` - 开始移动
- `movement:update` - 移动更新
- `movement:stop` - 停止移动

### 动作事件
- `action:jump` - 跳跃
- `action:attack` - 攻击
- `action:interact` - 交互
- `action:run` - 奔跑

### 相机事件
- `camera:rotate` - 相机旋转
- `camera:zoom` - 相机缩放

### UI事件
- `ui:menu` - 菜单
- `ui:inventory` - 背包
- `ui:chat` - 聊天
- `ui:map` - 地图

### 技能事件
- `skill:slot1` ~ `skill:slot9` - 技能槽1-9
- `skill:slot0` - 技能槽0

### 系统事件
- `system:pause` - 暂停
- `system:screenshot` - 截图

## 默认键位映射

### 移动控制
- `W` / `↑` - 向前移动
- `S` / `↓` - 向后移动
- `A` / `←` - 向左移动
- `D` / `→` - 向右移动

### 动作控制
- `空格` - 跳跃
- `Shift` - 奔跑
- `E` - 交互
- `F` - 攻击

### UI控制
- `ESC` - 菜单
- `I` - 背包
- `Enter` - 聊天
- `M` - 地图

### 技能快捷键
- `1-9, 0` - 技能槽

### 鼠标控制
- `左键` - 攻击
- `右键` - 交互
- `滚轮` - 缩放
- `移动` - 相机旋转

## 使用示例

### 基本使用

```javascript
// 1. 初始化系统
const inputManager = await initializeGlobalInputManager(scene);
const inputConfig = initializeGlobalInputConfig();

// 2. 监听事件
inputManager.on('action:jump', () => {
    console.log('玩家跳跃!');
});

// 3. 获取输入状态
const movement = inputManager.getMovementInput();
const isJumping = inputManager.isKeyPressed(' ');
```

### 自定义映射

```javascript
// 添加自定义键位
inputManager.setInputMapping('t', 'ui:chat');
inputConfig.setKeyboardMapping('t', 'ui:chat');

// 移除映射
inputManager.removeInputMapping('t');
inputConfig.removeKeyboardMapping('t');
```

### 配置管理

```javascript
// 修改敏感度
inputConfig.setSensitivity('mouseSensitivity', 2.0);

// 导出配置
const configJSON = inputConfig.exportToJSON();

// 导入配置
inputConfig.importFromJSON(configJSON);

// 验证配置
const validation = inputConfig.validateConfig();
```

### ECS集成

```javascript
// 创建玩家输入实体
const playerEntity = createPlayerInputEntity(ecsWorld, 0, 0, 0);

// 注册输入系统
ecsWorld.registerSystem(inputSystem, 'InputSystem');

// 启动ECS
ecsWorld.start();
```

## 高级功能

### 敏感度设置

```javascript
// 鼠标敏感度
inputConfig.setSensitivity('mouseSensitivity', 1.5);

// 缩放敏感度
inputConfig.setSensitivity('zoomSensitivity', 0.8);

// 相机旋转速度
inputConfig.setSensitivity('cameraRotationSpeed', 2.0);
```

### 设备配置

```javascript
// 鼠标设置
inputConfig.setDeviceSetting('mouse', 'invertY', true);
inputConfig.setDeviceSetting('mouse', 'smoothing', true);

// 键盘设置
inputConfig.setDeviceSetting('keyboard', 'repeatDelay', 300);
```

### 调试功能

```javascript
// 启用调试模式
inputManager.setDebugMode(true);
inputConfig.setAdvancedSetting('debugMode', true);

// 获取统计信息
const stats = inputManager.getStats();
console.log('输入统计:', stats);

// 获取配置统计
const configStats = inputConfig.getStats();
console.log('配置统计:', configStats);
```

## 最佳实践

### ✅ 推荐做法

1. **使用全局实例**: 通过全局函数获取输入管理器实例
2. **事件驱动**: 使用事件监听而不是轮询状态
3. **配置持久化**: 及时保存用户配置
4. **错误处理**: 包装输入操作在try-catch中
5. **资源清理**: 在场景销毁时清理输入系统

### ❌ 避免做法

1. **直接访问内部状态**: 使用公共API而不是内部属性
2. **忘记清理**: 不清理事件监听器会导致内存泄漏
3. **阻塞操作**: 在输入事件处理中避免长时间操作
4. **重复初始化**: 检查是否已初始化再创建实例

## 故障排除

### 常见问题

1. **输入无响应**
   - 检查输入管理器是否已初始化
   - 确认输入管理器已启用
   - 验证事件监听器是否正确设置

2. **键位映射不生效**
   - 检查映射是否正确设置
   - 确认事件类型拼写正确
   - 验证配置是否已保存

3. **性能问题**
   - 减少不必要的事件监听器
   - 使用防抖处理高频事件
   - 检查是否有内存泄漏

### 调试命令

```javascript
// 检查输入管理器状态
console.log(window.inputManager.getStats());

// 检查配置状态
console.log(window.inputConfig.getStats());

// 验证配置
console.log(window.inputConfig.validateConfig());

// 查看当前映射
console.log(window.inputManager.getInputMappings());
```

## 扩展开发

### 添加新事件类型

```javascript
// 在InputManager.js中添加新事件类型
export const InputEventTypes = {
    // 现有事件...
    CUSTOM_ACTION: 'custom:action'
};

// 设置映射
inputManager.setInputMapping('x', InputEventTypes.CUSTOM_ACTION);

// 监听事件
inputManager.on(InputEventTypes.CUSTOM_ACTION, (data) => {
    console.log('自定义动作!', data);
});
```

### 添加新设备支持

```javascript
// 扩展InputManager类
class ExtendedInputManager extends InputManager {
    initializeCustomDevice() {
        // 自定义设备初始化逻辑
    }
    
    processCustomInput(data) {
        // 自定义输入处理逻辑
    }
}
```

## 更多资源

- [输入系统API文档](./input-system-api.md)
- [使用示例代码](../src/examples/input-usage-example.js)
- [单元测试](../src/core/InputManager.unit.test.js)
- [ECS集成指南](./ecs/bitECS-integration-guide.md)
