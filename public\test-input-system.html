<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>输入系统测试 - Cantos MMORPG</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffd700;
        }
        
        .key-indicator {
            display: inline-block;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            padding: 5px 10px;
            margin: 2px;
            min-width: 30px;
            text-align: center;
            transition: all 0.2s ease;
        }
        
        .key-indicator.active {
            background: #4CAF50;
            border-color: #45a049;
            transform: scale(1.1);
            box-shadow: 0 0 10px rgba(76, 175, 80, 0.5);
        }
        
        .event-log {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 5px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .event-entry {
            margin-bottom: 5px;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .event-movement { background: rgba(33, 150, 243, 0.2); }
        .event-action { background: rgba(255, 152, 0, 0.2); }
        .event-ui { background: rgba(156, 39, 176, 0.2); }
        .event-skill { background: rgba(244, 67, 54, 0.2); }
        
        .instructions {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }
        
        .instructions h3 {
            color: #ffd700;
            margin-top: 0;
        }
        
        .instructions ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .stats-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
            padding: 10px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Cantos MMORPG 输入系统测试</h1>
            <p>测试新的输入管理器功能 - 按键、鼠标和事件处理</p>
        </div>
        
        <div class="test-panel">
            <h2>🎯 实时输入状态</h2>
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-title">移动控制</div>
                    <div id="movement-keys">
                        <div class="key-indicator" data-key="w">W</div>
                        <div class="key-indicator" data-key="a">A</div>
                        <div class="key-indicator" data-key="s">S</div>
                        <div class="key-indicator" data-key="d">D</div>
                        <div class="key-indicator" data-key="arrowup">↑</div>
                        <div class="key-indicator" data-key="arrowdown">↓</div>
                        <div class="key-indicator" data-key="arrowleft">←</div>
                        <div class="key-indicator" data-key="arrowright">→</div>
                    </div>
                </div>
                
                <div class="status-card">
                    <div class="status-title">动作控制</div>
                    <div id="action-keys">
                        <div class="key-indicator" data-key=" ">空格</div>
                        <div class="key-indicator" data-key="shift">Shift</div>
                        <div class="key-indicator" data-key="e">E</div>
                        <div class="key-indicator" data-key="f">F</div>
                    </div>
                </div>
                
                <div class="status-card">
                    <div class="status-title">UI控制</div>
                    <div id="ui-keys">
                        <div class="key-indicator" data-key="escape">ESC</div>
                        <div class="key-indicator" data-key="i">I</div>
                        <div class="key-indicator" data-key="enter">Enter</div>
                        <div class="key-indicator" data-key="m">M</div>
                    </div>
                </div>
                
                <div class="status-card">
                    <div class="status-title">技能快捷键</div>
                    <div id="skill-keys">
                        <div class="key-indicator" data-key="1">1</div>
                        <div class="key-indicator" data-key="2">2</div>
                        <div class="key-indicator" data-key="3">3</div>
                        <div class="key-indicator" data-key="4">4</div>
                        <div class="key-indicator" data-key="5">5</div>
                        <div class="key-indicator" data-key="6">6</div>
                        <div class="key-indicator" data-key="7">7</div>
                        <div class="key-indicator" data-key="8">8</div>
                        <div class="key-indicator" data-key="9">9</div>
                        <div class="key-indicator" data-key="0">0</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-panel">
            <h2>📊 输入统计</h2>
            <div class="stats-display" id="stats-display">
                <div class="stat-item">
                    <div class="stat-value" id="key-presses">0</div>
                    <div class="stat-label">按键次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="mouse-clicks">0</div>
                    <div class="stat-label">鼠标点击</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="movement-events">0</div>
                    <div class="stat-label">移动事件</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="total-events">0</div>
                    <div class="stat-label">总事件数</div>
                </div>
            </div>
        </div>
        
        <div class="test-panel">
            <h2>📝 事件日志</h2>
            <div class="event-log" id="event-log">
                <div class="event-entry">等待输入事件...</div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎮 测试说明</h3>
            <ul>
                <li><strong>移动测试</strong>: 按 WASD 或方向键测试移动输入</li>
                <li><strong>动作测试</strong>: 按空格键(跳跃)、Shift(奔跑)、E(交互)、F(攻击)</li>
                <li><strong>UI测试</strong>: 按 ESC(菜单)、I(背包)、Enter(聊天)、M(地图)</li>
                <li><strong>技能测试</strong>: 按数字键 0-9 测试技能快捷键</li>
                <li><strong>鼠标测试</strong>: 点击鼠标左键、右键，滚动滚轮</li>
                <li><strong>调试</strong>: 打开浏览器控制台查看详细日志</li>
            </ul>
            <p><strong>注意</strong>: 确保页面已获得焦点，点击页面任意位置开始测试。</p>
        </div>
    </div>
    
    <script>
        // 统计数据
        let stats = {
            keyPresses: 0,
            mouseClicks: 0,
            movementEvents: 0,
            totalEvents: 0
        };
        
        // 更新统计显示
        function updateStats() {
            document.getElementById('key-presses').textContent = stats.keyPresses;
            document.getElementById('mouse-clicks').textContent = stats.mouseClicks;
            document.getElementById('movement-events').textContent = stats.movementEvents;
            document.getElementById('total-events').textContent = stats.totalEvents;
        }
        
        // 添加事件日志
        function addEventLog(message, type = '') {
            const log = document.getElementById('event-log');
            const entry = document.createElement('div');
            entry.className = `event-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
            
            // 限制日志条数
            if (log.children.length > 100) {
                log.removeChild(log.firstChild);
            }
        }
        
        // 更新按键状态显示
        function updateKeyState(key, pressed) {
            const indicator = document.querySelector(`[data-key="${key}"]`);
            if (indicator) {
                if (pressed) {
                    indicator.classList.add('active');
                } else {
                    indicator.classList.remove('active');
                }
            }
        }
        
        // 键盘事件监听
        document.addEventListener('keydown', (event) => {
            const key = event.key.toLowerCase();
            updateKeyState(key, true);
            stats.keyPresses++;
            stats.totalEvents++;
            updateStats();
            
            let eventType = 'event-action';
            if (['w', 'a', 's', 'd', 'arrowup', 'arrowdown', 'arrowleft', 'arrowright'].includes(key)) {
                eventType = 'event-movement';
                stats.movementEvents++;
            } else if (['escape', 'i', 'enter', 'm'].includes(key)) {
                eventType = 'event-ui';
            } else if (['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'].includes(key)) {
                eventType = 'event-skill';
            }
            
            addEventLog(`按键按下: ${key}`, eventType);
        });
        
        document.addEventListener('keyup', (event) => {
            const key = event.key.toLowerCase();
            updateKeyState(key, false);
            addEventLog(`按键释放: ${key}`, 'event-action');
        });
        
        // 鼠标事件监听
        document.addEventListener('mousedown', (event) => {
            stats.mouseClicks++;
            stats.totalEvents++;
            updateStats();
            addEventLog(`鼠标按下: 按钮${event.button}`, 'event-action');
        });
        
        document.addEventListener('mouseup', (event) => {
            addEventLog(`鼠标释放: 按钮${event.button}`, 'event-action');
        });
        
        document.addEventListener('wheel', (event) => {
            stats.totalEvents++;
            updateStats();
            const direction = event.deltaY > 0 ? '向下' : '向上';
            addEventLog(`鼠标滚轮: ${direction}`, 'event-action');
        });
        
        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            addEventLog('输入系统测试页面已加载', 'event-ui');
            addEventLog('点击页面获得焦点，然后开始测试', 'event-ui');
            
            // 检查是否有全局输入管理器
            setTimeout(() => {
                if (window.inputManager) {
                    addEventLog('✅ 检测到全局输入管理器', 'event-ui');
                    addEventLog('🎮 输入系统已就绪', 'event-ui');
                } else {
                    addEventLog('⚠️ 未检测到全局输入管理器', 'event-ui');
                    addEventLog('请确保主游戏页面已加载', 'event-ui');
                }
                
                if (window.inputConfig) {
                    addEventLog('✅ 检测到全局输入配置', 'event-ui');
                } else {
                    addEventLog('⚠️ 未检测到全局输入配置', 'event-ui');
                }
            }, 1000);
        });
        
        // 页面获得焦点时的提示
        document.addEventListener('click', () => {
            addEventLog('页面已获得焦点，可以开始测试', 'event-ui');
        }, { once: true });
        
        console.log('🎮 输入系统测试页面已初始化');
    </script>
</body>
</html>
