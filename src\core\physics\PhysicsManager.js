/**
 * 物理系统管理器 (PhysicsManager)
 * 为Cantos MMORPG项目提供完整的Havok物理引擎集成
 * 支持刚体创建、触发器、射线检测等核心物理功能
 */

import { Vector3, Quaternion } from "@babylonjs/core/Maths/math.vector";
import { HavokPlugin } from "@babylonjs/core/Physics/v2/Plugins/havokPlugin";
import { PhysicsBody } from "@babylonjs/core/Physics/v2/physicsBody";
import { PhysicsMotionType } from "@babylonjs/core/Physics/v2/IPhysicsEnginePlugin";
import {
    PhysicsShapeBox,
    PhysicsShapeSphere,
    PhysicsShapeCapsule,
    PhysicsShapeConvexHull,
    PhysicsShapeMesh
} from "@babylonjs/core/Physics/v2/physicsShape";
import HavokPhysics from "@babylonjs/havok";
import "@babylonjs/core/Physics/physicsEngineComponent";

/**
 * 物理材质预设
 */
export const PhysicsMaterialPresets = {
    // 默认材质
    DEFAULT: {
        friction: 0.5,
        restitution: 0.3
    },
    // 冰面材质 - 低摩擦
    ICE: {
        friction: 0.1,
        restitution: 0.2
    },
    // 橡胶材质 - 高弹性
    RUBBER: {
        friction: 0.8,
        restitution: 0.9
    },
    // 金属材质
    METAL: {
        friction: 0.4,
        restitution: 0.1
    },
    // 木材材质
    WOOD: {
        friction: 0.6,
        restitution: 0.4
    },
    // 石头材质
    STONE: {
        friction: 0.7,
        restitution: 0.2
    },
    // 玩家角色材质
    PLAYER: {
        friction: 0.8,
        restitution: 0.1
    }
};

/**
 * 物理形状类型枚举
 */
export const PhysicsShapeTypes = {
    BOX: 'box',
    SPHERE: 'sphere',
    CAPSULE: 'capsule',
    CONVEX_HULL: 'convexHull',
    MESH: 'mesh'
};

/**
 * 物理系统管理器类
 * 提供完整的物理引擎管理功能
 */
export class PhysicsManager {
    constructor(scene) {
        this.scene = scene;
        this.havokInstance = null;
        this.havokPlugin = null;
        this.isInitialized = false;

        // 物理体注册表
        this.physicsBodies = new Map();
        this.triggers = new Map();

        // 性能统计
        this.stats = {
            rigidBodiesCount: 0,
            triggersCount: 0,
            raycastsPerFrame: 0,
            lastUpdateTime: 0
        };

        // 配置选项
        this.config = {
            gravity: new Vector3(0, -9.81, 0),
            enableDebugDraw: false,
            substeps: 1,
            maxSubsteps: 4,
            fixedTimeStep: 1/60
        };

        console.log('物理系统管理器已创建');
    }

    /**
     * 初始化物理引擎
     * @param {Object} options - 初始化选项
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async initialize(options = {}) {
        console.log('正在初始化Havok物理引擎...');

        try {
            // 合并配置选项
            this.config = { ...this.config, ...options };

            // 初始化Havok实例
            this.havokInstance = await HavokPhysics();

            // 创建Havok插件
            this.havokPlugin = new HavokPlugin(true, this.havokInstance);

            // 启用物理引擎
            this.scene.enablePhysics(this.config.gravity, this.havokPlugin);

            // 设置物理引擎参数
            if (this.scene.getPhysicsEngine()) {
                const engine = this.scene.getPhysicsEngine();
                engine.setSubTimeStep(this.config.fixedTimeStep);
                engine.setMaxSubSteps(this.config.maxSubsteps);
            }

            this.isInitialized = true;
            console.log('✅ Havok物理引擎初始化完成');

            return true;
        } catch (error) {
            console.error('❌ 物理引擎初始化失败:', error);
            this.isInitialized = false;
            return false;
        }
    }

    /**
     * 检查物理引擎是否已初始化
     * @returns {boolean}
     */
    isReady() {
        return this.isInitialized && this.scene.getPhysicsEngine() !== null;
    }

    /**
     * 设置重力
     * @param {Vector3} gravity - 重力向量
     */
    setGravity(gravity) {
        if (!this.isReady()) {
            console.warn('物理引擎未初始化，无法设置重力');
            return;
        }

        this.config.gravity = gravity;
        this.scene.getPhysicsEngine().setGravity(gravity);
        console.log(`重力已设置为: ${gravity.toString()}`);
    }

    /**
     * 获取当前重力设置
     * @returns {Vector3}
     */
    getGravity() {
        return this.config.gravity.clone();
    }

    /**
     * 创建物理形状
     * @param {string} shapeType - 形状类型
     * @param {Object} options - 形状选项
     * @returns {PhysicsShape} 物理形状实例
     */
    createPhysicsShape(shapeType, options = {}) {
        if (!this.isReady()) {
            console.warn('物理引擎未初始化，无法创建物理形状');
            return null;
        }

        const {
            center = Vector3.Zero(),
            rotation = Quaternion.Identity(),
            material = PhysicsMaterialPresets.DEFAULT
        } = options;

        let shape = null;

        switch (shapeType) {
            case PhysicsShapeTypes.BOX:
                const { size = new Vector3(1, 1, 1) } = options;
                shape = new PhysicsShapeBox(center, rotation, size, this.scene);
                break;

            case PhysicsShapeTypes.SPHERE:
                const { radius = 1 } = options;
                shape = new PhysicsShapeSphere(center, radius, this.scene);
                break;

            case PhysicsShapeTypes.CAPSULE:
                const {
                    pointA = new Vector3(0, -0.5, 0),
                    pointB = new Vector3(0, 0.5, 0),
                    radius: capsuleRadius = 0.5
                } = options;
                shape = new PhysicsShapeCapsule(pointA, pointB, capsuleRadius, this.scene);
                break;

            case PhysicsShapeTypes.CONVEX_HULL:
                const { mesh } = options;
                if (!mesh) {
                    console.error('创建凸包形状需要提供mesh参数');
                    return null;
                }
                shape = new PhysicsShapeConvexHull(mesh, this.scene);
                break;

            case PhysicsShapeTypes.MESH:
                const { mesh: meshShape } = options;
                if (!meshShape) {
                    console.error('创建网格形状需要提供mesh参数');
                    return null;
                }
                shape = new PhysicsShapeMesh(meshShape, this.scene);
                break;

            default:
                console.error(`不支持的物理形状类型: ${shapeType}`);
                return null;
        }

        // 设置材质属性
        if (shape && material) {
            shape.material = material;
        }

        return shape;
    }

    /**
     * 创建刚体
     * @param {Mesh} mesh - 关联的网格对象
     * @param {Object} options - 刚体选项
     * @returns {PhysicsBody} 物理体实例
     */
    createRigidBody(mesh, options = {}) {
        if (!this.isReady()) {
            console.warn('物理引擎未初始化，无法创建刚体');
            return null;
        }

        const {
            motionType = PhysicsMotionType.DYNAMIC,
            mass = 1,
            shapeType = PhysicsShapeTypes.BOX,
            shapeOptions = {},
            material = PhysicsMaterialPresets.DEFAULT,
            startAsleep = false
        } = options;

        try {
            // 创建物理体
            const physicsBody = new PhysicsBody(mesh, motionType, startAsleep, this.scene);

            // 创建物理形状
            const shape = this.createPhysicsShape(shapeType, {
                ...shapeOptions,
                material
            });

            if (!shape) {
                console.error('创建物理形状失败');
                return null;
            }

            // 关联形状和物理体
            physicsBody.shape = shape;

            // 设置质量属性
            physicsBody.setMassProperties({ mass });

            // 注册物理体
            const bodyId = this.generateBodyId();
            this.physicsBodies.set(bodyId, {
                body: physicsBody,
                mesh: mesh,
                options: options
            });

            // 更新统计
            this.stats.rigidBodiesCount++;

            console.log(`创建刚体成功: ${bodyId}, 类型: ${shapeType}, 质量: ${mass}`);

            return physicsBody;
        } catch (error) {
            console.error('创建刚体失败:', error);
            return null;
        }
    }

    /**
     * 创建触发器
     * @param {Mesh} mesh - 关联的网格对象
     * @param {Function} callback - 触发回调函数
     * @param {Object} options - 触发器选项
     * @returns {PhysicsBody} 触发器物理体
     */
    createTrigger(mesh, callback, options = {}) {
        if (!this.isReady()) {
            console.warn('物理引擎未初始化，无法创建触发器');
            return null;
        }

        const {
            shapeType = PhysicsShapeTypes.BOX,
            shapeOptions = {}
        } = options;

        try {
            // 创建静态物理体作为触发器
            const triggerBody = new PhysicsBody(mesh, PhysicsMotionType.STATIC, false, this.scene);

            // 创建物理形状
            const shape = this.createPhysicsShape(shapeType, shapeOptions);
            if (!shape) {
                console.error('创建触发器形状失败');
                return null;
            }

            // 关联形状
            triggerBody.shape = shape;

            // 设置为触发器
            triggerBody.setTrigger(true);

            // 注册触发器
            const triggerId = this.generateTriggerId();
            this.triggers.set(triggerId, {
                body: triggerBody,
                mesh: mesh,
                callback: callback,
                options: options
            });

            // 设置碰撞事件监听
            triggerBody.setCollisionCallbackEnabled(true);
            triggerBody.getCollisionObservable().add((collisionEvent) => {
                if (callback && typeof callback === 'function') {
                    callback(collisionEvent, triggerBody);
                }
            });

            // 更新统计
            this.stats.triggersCount++;

            console.log(`创建触发器成功: ${triggerId}`);

            return triggerBody;
        } catch (error) {
            console.error('创建触发器失败:', error);
            return null;
        }
    }

    /**
     * 射线检测
     * @param {Vector3} origin - 射线起点
     * @param {Vector3} direction - 射线方向
     * @param {number} distance - 射线距离
     * @param {Object} options - 射线检测选项
     * @returns {PhysicsRaycastResult|null} 射线检测结果
     */
    raycast(origin, direction, distance = 100, options = {}) {
        if (!this.isReady()) {
            console.warn('物理引擎未初始化，无法进行射线检测');
            return null;
        }

        const {
            ignoreList = [],
            includeList = null
        } = options;

        try {
            // 更新射线检测统计
            this.stats.raycastsPerFrame++;

            // 执行射线检测
            const physicsEngine = this.scene.getPhysicsEngine();
            const raycastResult = physicsEngine.raycast(origin, direction.scale(distance));

            // 过滤结果
            if (raycastResult && raycastResult.hasHit) {
                const hitBody = raycastResult.body;

                // 检查忽略列表
                if (ignoreList.length > 0) {
                    for (const ignoreBody of ignoreList) {
                        if (hitBody === ignoreBody) {
                            return null;
                        }
                    }
                }

                // 检查包含列表
                if (includeList && includeList.length > 0) {
                    let found = false;
                    for (const includeBody of includeList) {
                        if (hitBody === includeBody) {
                            found = true;
                            break;
                        }
                    }
                    if (!found) {
                        return null;
                    }
                }
            }

            return raycastResult;
        } catch (error) {
            console.error('射线检测失败:', error);
            return null;
        }
    }

    /**
     * 应用力到物理体
     * @param {PhysicsBody} physicsBody - 物理体
     * @param {Vector3} force - 力向量
     * @param {Vector3} contactPoint - 作用点（可选）
     */
    applyForce(physicsBody, force, contactPoint = null) {
        if (!this.isReady()) {
            console.warn('物理引擎未初始化，无法应用力');
            return;
        }

        try {
            if (contactPoint) {
                physicsBody.applyForce(force, contactPoint);
            } else {
                physicsBody.applyForce(force, physicsBody.transformNode.getAbsolutePosition());
            }
        } catch (error) {
            console.error('应用力失败:', error);
        }
    }

    /**
     * 应用冲量到物理体
     * @param {PhysicsBody} physicsBody - 物理体
     * @param {Vector3} impulse - 冲量向量
     * @param {Vector3} contactPoint - 作用点（可选）
     */
    applyImpulse(physicsBody, impulse, contactPoint = null) {
        if (!this.isReady()) {
            console.warn('物理引擎未初始化，无法应用冲量');
            return;
        }

        try {
            if (contactPoint) {
                physicsBody.applyImpulse(impulse, contactPoint);
            } else {
                physicsBody.applyImpulse(impulse, physicsBody.transformNode.getAbsolutePosition());
            }
        } catch (error) {
            console.error('应用冲量失败:', error);
        }
    }

    /**
     * 获取物理体速度
     * @param {PhysicsBody} physicsBody - 物理体
     * @returns {Vector3} 速度向量
     */
    getVelocity(physicsBody) {
        if (!physicsBody) {
            return Vector3.Zero();
        }

        try {
            return physicsBody.getLinearVelocity();
        } catch (error) {
            console.error('获取速度失败:', error);
            return Vector3.Zero();
        }
    }

    /**
     * 设置物理体速度
     * @param {PhysicsBody} physicsBody - 物理体
     * @param {Vector3} velocity - 速度向量
     */
    setVelocity(physicsBody, velocity) {
        if (!physicsBody) {
            return;
        }

        try {
            physicsBody.setLinearVelocity(velocity);
        } catch (error) {
            console.error('设置速度失败:', error);
        }
    }

    /**
     * 生成物理体ID
     * @returns {string}
     */
    generateBodyId() {
        return `physics_body_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 生成触发器ID
     * @returns {string}
     */
    generateTriggerId() {
        return `trigger_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 移除物理体
     * @param {PhysicsBody} physicsBody - 要移除的物理体
     */
    removeRigidBody(physicsBody) {
        if (!physicsBody) {
            return;
        }

        try {
            // 从注册表中移除
            for (const [id, data] of this.physicsBodies.entries()) {
                if (data.body === physicsBody) {
                    this.physicsBodies.delete(id);
                    this.stats.rigidBodiesCount--;
                    console.log(`移除刚体: ${id}`);
                    break;
                }
            }

            // 销毁物理体
            physicsBody.dispose();
        } catch (error) {
            console.error('移除刚体失败:', error);
        }
    }

    /**
     * 移除触发器
     * @param {PhysicsBody} triggerBody - 要移除的触发器
     */
    removeTrigger(triggerBody) {
        if (!triggerBody) {
            return;
        }

        try {
            // 从注册表中移除
            for (const [id, data] of this.triggers.entries()) {
                if (data.body === triggerBody) {
                    this.triggers.delete(id);
                    this.stats.triggersCount--;
                    console.log(`移除触发器: ${id}`);
                    break;
                }
            }

            // 销毁触发器
            triggerBody.dispose();
        } catch (error) {
            console.error('移除触发器失败:', error);
        }
    }

    /**
     * 获取性能统计信息
     * @returns {Object} 性能统计数据
     */
    getStats() {
        return {
            ...this.stats,
            isInitialized: this.isInitialized,
            gravity: this.config.gravity.toString(),
            physicsEngineType: 'Havok'
        };
    }

    /**
     * 重置性能统计
     */
    resetStats() {
        this.stats.raycastsPerFrame = 0;
        this.stats.lastUpdateTime = performance.now();
    }

    /**
     * 更新物理系统（每帧调用）
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (!this.isReady()) {
            return;
        }

        // 重置每帧统计
        this.stats.raycastsPerFrame = 0;
        this.stats.lastUpdateTime = performance.now();

        // 这里可以添加其他每帧更新逻辑
        // 例如：物理体状态检查、性能监控等
    }

    /**
     * 启用调试绘制
     * @param {boolean} enable - 是否启用
     */
    setDebugDraw(enable) {
        this.config.enableDebugDraw = enable;

        if (this.isReady()) {
            const physicsEngine = this.scene.getPhysicsEngine();
            if (physicsEngine && physicsEngine.setDebugMode) {
                physicsEngine.setDebugMode(enable);
            }
        }

        console.log(`物理调试绘制已${enable ? '启用' : '禁用'}`);
    }

    /**
     * 暂停物理模拟
     */
    pause() {
        if (this.isReady()) {
            const physicsEngine = this.scene.getPhysicsEngine();
            if (physicsEngine) {
                physicsEngine.setEnabled(false);
                console.log('物理模拟已暂停');
            }
        }
    }

    /**
     * 恢复物理模拟
     */
    resume() {
        if (this.isReady()) {
            const physicsEngine = this.scene.getPhysicsEngine();
            if (physicsEngine) {
                physicsEngine.setEnabled(true);
                console.log('物理模拟已恢复');
            }
        }
    }

    /**
     * 清理所有物理资源
     */
    dispose() {
        console.log('正在清理物理系统资源...');

        try {
            // 清理所有刚体
            for (const [id, data] of this.physicsBodies.entries()) {
                try {
                    data.body.dispose();
                } catch (error) {
                    console.warn(`清理刚体 ${id} 失败:`, error);
                }
            }
            this.physicsBodies.clear();

            // 清理所有触发器
            for (const [id, data] of this.triggers.entries()) {
                try {
                    data.body.dispose();
                } catch (error) {
                    console.warn(`清理触发器 ${id} 失败:`, error);
                }
            }
            this.triggers.clear();

            // 禁用物理引擎
            if (this.scene && this.scene.getPhysicsEngine()) {
                this.scene.disablePhysicsEngine();
            }

            // 重置状态
            this.isInitialized = false;
            this.havokInstance = null;
            this.havokPlugin = null;

            // 重置统计
            this.stats = {
                rigidBodiesCount: 0,
                triggersCount: 0,
                raycastsPerFrame: 0,
                lastUpdateTime: 0
            };

            console.log('✅ 物理系统资源清理完成');
        } catch (error) {
            console.error('❌ 物理系统资源清理失败:', error);
        }
    }
}

// 导出默认实例
export default PhysicsManager;

console.log('PhysicsManager模块已加载');
