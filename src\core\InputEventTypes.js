/**
 * 输入事件类型定义
 * 为Cantos MMORPG输入系统提供统一的事件类型常量
 */

/**
 * 输入事件类型枚举
 */
export const InputEventTypes = {
    // 移动相关
    MOVEMENT_START: 'movement:start',
    MOVEMENT_UPDATE: 'movement:update', 
    MOVEMENT_STOP: 'movement:stop',
    
    // 动作相关
    ACTION_JUMP: 'action:jump',
    ACTION_ATTACK: 'action:attack',
    ACTION_INTERACT: 'action:interact',
    ACTION_RUN: 'action:run',
    
    // 相机相关
    CAMERA_ROTATE: 'camera:rotate',
    CAMERA_ZOOM: 'camera:zoom',
    
    // UI相关
    UI_MENU: 'ui:menu',
    UI_INVENTORY: 'ui:inventory',
    UI_CHAT: 'ui:chat',
    UI_MAP: 'ui:map',
    
    // 技能相关
    SKILL_SLOT_1: 'skill:slot1',
    SKILL_SLOT_2: 'skill:slot2',
    SKILL_SLOT_3: 'skill:slot3',
    SKILL_SLOT_4: 'skill:slot4',
    SKILL_SLOT_5: 'skill:slot5',
    SKILL_SLOT_6: 'skill:slot6',
    SKILL_SLOT_7: 'skill:slot7',
    SKILL_SLOT_8: 'skill:slot8',
    SKILL_SLOT_9: 'skill:slot9',
    SKILL_SLOT_0: 'skill:slot0',
    
    // 系统相关
    SYSTEM_PAUSE: 'system:pause',
    SYSTEM_SCREENSHOT: 'system:screenshot'
};

/**
 * 输入设备类型
 */
export const InputDeviceTypes = {
    KEYBOARD: 'keyboard',
    MOUSE: 'mouse', 
    TOUCH: 'touch',
    GAMEPAD: 'gamepad'
};

console.log('🎮 输入事件类型模块已加载');
