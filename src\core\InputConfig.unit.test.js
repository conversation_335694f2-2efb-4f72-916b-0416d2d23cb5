/**
 * 输入配置管理器单元测试
 * 测试InputConfig的核心功能
 */

import { jest } from '@jest/globals';
import { InputConfig, DefaultInputConfig } from './InputConfig.js';
import { InputEventTypes } from './InputManager.js';

// Mock localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};

Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
});

describe('InputConfig', () => {
    let inputConfig;
    
    beforeEach(() => {
        // 重置所有mock
        jest.clearAllMocks();
        
        // 创建输入配置实例
        inputConfig = new InputConfig();
    });
    
    describe('构造函数', () => {
        test('应该使用默认配置初始化', () => {
            expect(inputConfig.config).toEqual(DefaultInputConfig);
        });
        
        test('应该合并用户配置', () => {
            const userConfig = {
                sensitivity: {
                    mouseSensitivity: 2.0
                },
                advanced: {
                    debugMode: true
                }
            };
            
            const config = new InputConfig(userConfig);
            
            expect(config.config.sensitivity.mouseSensitivity).toBe(2.0);
            expect(config.config.advanced.debugMode).toBe(true);
            // 其他默认值应该保持不变
            expect(config.config.sensitivity.zoomSensitivity).toBe(DefaultInputConfig.sensitivity.zoomSensitivity);
        });
    });
    
    describe('配置合并', () => {
        test('应该正确深度合并配置', () => {
            const defaultConfig = {
                a: { b: { c: 1, d: 2 } },
                e: 3
            };
            
            const userConfig = {
                a: { b: { c: 10 } },
                f: 4
            };
            
            const merged = inputConfig.mergeConfigs(defaultConfig, userConfig);
            
            expect(merged.a.b.c).toBe(10);  // 用户值
            expect(merged.a.b.d).toBe(2);   // 默认值保留
            expect(merged.e).toBe(3);       // 默认值保留
            expect(merged.f).toBe(4);       // 新增用户值
        });
    });
    
    describe('本地存储', () => {
        test('应该能够保存到本地存储', () => {
            const result = inputConfig.saveToStorage();
            
            expect(result).toBe(true);
            expect(localStorageMock.setItem).toHaveBeenCalledWith(
                'cantos_input_config',
                JSON.stringify(inputConfig.config)
            );
        });
        
        test('应该能够从本地存储加载', () => {
            const storedConfig = {
                sensitivity: { mouseSensitivity: 3.0 }
            };
            
            localStorageMock.getItem.mockReturnValue(JSON.stringify(storedConfig));
            
            const result = inputConfig.loadFromStorage();
            
            expect(result).toBe(true);
            expect(localStorageMock.getItem).toHaveBeenCalledWith('cantos_input_config');
            expect(inputConfig.config.sensitivity.mouseSensitivity).toBe(3.0);
        });
        
        test('加载失败时应该返回false', () => {
            localStorageMock.getItem.mockReturnValue(null);
            
            const result = inputConfig.loadFromStorage();
            
            expect(result).toBe(false);
        });
        
        test('保存失败时应该返回false', () => {
            localStorageMock.setItem.mockImplementation(() => {
                throw new Error('Storage error');
            });
            
            const result = inputConfig.saveToStorage();
            
            expect(result).toBe(false);
        });
    });
    
    describe('配置重置', () => {
        test('应该能够重置为默认配置', () => {
            // 修改配置
            inputConfig.setSensitivity('mouseSensitivity', 5.0);
            expect(inputConfig.config.sensitivity.mouseSensitivity).toBe(5.0);
            
            // 重置
            inputConfig.resetToDefault();
            expect(inputConfig.config.sensitivity.mouseSensitivity).toBe(DefaultInputConfig.sensitivity.mouseSensitivity);
        });
    });
    
    describe('获取配置', () => {
        test('应该返回完整配置的副本', () => {
            const config = inputConfig.getConfig();
            
            expect(config).toEqual(inputConfig.config);
            expect(config).not.toBe(inputConfig.config); // 应该是副本
        });
        
        test('应该返回键盘映射', () => {
            const mappings = inputConfig.getKeyboardMappings();
            
            expect(mappings).toEqual(inputConfig.config.keyboardMappings);
            expect(mappings).not.toBe(inputConfig.config.keyboardMappings);
        });
        
        test('应该返回鼠标映射', () => {
            const mappings = inputConfig.getMouseMappings();
            
            expect(mappings).toEqual(inputConfig.config.mouseMappings);
            expect(mappings).not.toBe(inputConfig.config.mouseMappings);
        });
        
        test('应该返回敏感度设置', () => {
            const sensitivity = inputConfig.getSensitivity();
            
            expect(sensitivity).toEqual(inputConfig.config.sensitivity);
            expect(sensitivity).not.toBe(inputConfig.config.sensitivity);
        });
    });
    
    describe('设置配置', () => {
        test('应该能够设置键盘映射', () => {
            inputConfig.setKeyboardMapping('t', InputEventTypes.UI_CHAT, { test: true });
            
            const mapping = inputConfig.config.keyboardMappings['t'];
            expect(mapping.eventType).toBe(InputEventTypes.UI_CHAT);
            expect(mapping.data.test).toBe(true);
        });
        
        test('应该能够移除键盘映射', () => {
            inputConfig.setKeyboardMapping('t', InputEventTypes.UI_CHAT);
            expect(inputConfig.config.keyboardMappings['t']).toBeDefined();
            
            inputConfig.removeKeyboardMapping('t');
            expect(inputConfig.config.keyboardMappings['t']).toBeUndefined();
        });
        
        test('应该能够设置鼠标映射', () => {
            inputConfig.setMouseMapping('mouse:1', InputEventTypes.ACTION_ATTACK);
            
            const mapping = inputConfig.config.mouseMappings['mouse:1'];
            expect(mapping.eventType).toBe(InputEventTypes.ACTION_ATTACK);
        });
        
        test('应该能够设置敏感度', () => {
            inputConfig.setSensitivity('mouseSensitivity', 2.5);
            
            expect(inputConfig.config.sensitivity.mouseSensitivity).toBe(2.5);
        });
        
        test('敏感度应该限制在有效范围内', () => {
            inputConfig.setSensitivity('mouseSensitivity', 10.0);
            expect(inputConfig.config.sensitivity.mouseSensitivity).toBe(5.0);
            
            inputConfig.setSensitivity('mouseSensitivity', 0.05);
            expect(inputConfig.config.sensitivity.mouseSensitivity).toBe(0.1);
        });
        
        test('应该能够设置设备配置', () => {
            inputConfig.setDeviceSetting('mouse', 'invertY', true);
            
            expect(inputConfig.config.devices.mouse.invertY).toBe(true);
        });
        
        test('应该能够设置高级配置', () => {
            inputConfig.setAdvancedSetting('debugMode', true);
            
            expect(inputConfig.config.advanced.debugMode).toBe(true);
        });
    });
    
    describe('配置验证', () => {
        test('应该验证有效配置', () => {
            const result = inputConfig.validateConfig();
            
            expect(result.valid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });
        
        test('应该检测无效敏感度', () => {
            inputConfig.config.sensitivity.mouseSensitivity = 'invalid';
            
            const result = inputConfig.validateConfig();
            
            expect(result.valid).toBe(false);
            expect(result.errors.length).toBeGreaterThan(0);
        });
        
        test('应该检测缺失的映射', () => {
            inputConfig.config.keyboardMappings = {};
            inputConfig.config.mouseMappings = {};
            
            const result = inputConfig.validateConfig();
            
            expect(result.warnings.length).toBeGreaterThan(0);
        });
    });
    
    describe('导入导出', () => {
        test('应该能够导出为JSON', () => {
            const json = inputConfig.exportToJSON();
            
            expect(typeof json).toBe('string');
            expect(JSON.parse(json)).toEqual(inputConfig.config);
        });
        
        test('应该能够从JSON导入', () => {
            const testConfig = {
                sensitivity: { mouseSensitivity: 4.0 }
            };
            
            const json = JSON.stringify(testConfig);
            const result = inputConfig.importFromJSON(json);
            
            expect(result).toBe(true);
            expect(inputConfig.config.sensitivity.mouseSensitivity).toBe(4.0);
        });
        
        test('导入无效JSON应该失败', () => {
            const result = inputConfig.importFromJSON('invalid json');
            
            expect(result).toBe(false);
        });
    });
    
    describe('变更监听', () => {
        test('应该能够添加变更监听器', () => {
            const listener = jest.fn();
            
            inputConfig.addChangeListener(listener);
            inputConfig.setSensitivity('mouseSensitivity', 2.0);
            
            expect(listener).toHaveBeenCalledWith(
                'sensitivity',
                { type: 'mouseSensitivity', value: 2.0 },
                inputConfig.config
            );
        });
        
        test('应该能够移除变更监听器', () => {
            const listener = jest.fn();
            
            inputConfig.addChangeListener(listener);
            inputConfig.removeChangeListener(listener);
            inputConfig.setSensitivity('mouseSensitivity', 2.0);
            
            expect(listener).not.toHaveBeenCalled();
        });
        
        test('监听器错误不应该影响其他监听器', () => {
            const errorListener = jest.fn(() => {
                throw new Error('Listener error');
            });
            const normalListener = jest.fn();
            
            inputConfig.addChangeListener(errorListener);
            inputConfig.addChangeListener(normalListener);
            
            // 应该不会抛出错误
            expect(() => {
                inputConfig.setSensitivity('mouseSensitivity', 2.0);
            }).not.toThrow();
            
            expect(normalListener).toHaveBeenCalled();
        });
    });
    
    describe('统计信息', () => {
        test('应该返回正确的统计信息', () => {
            const stats = inputConfig.getStats();
            
            expect(stats.keyboardMappings).toBeGreaterThan(0);
            expect(stats.mouseMappings).toBeGreaterThan(0);
            expect(stats.enabledDevices).toContain('keyboard');
            expect(stats.enabledDevices).toContain('mouse');
            expect(typeof stats.debugMode).toBe('boolean');
        });
    });
});

describe('全局输入配置', () => {
    test('应该能够初始化全局实例', async () => {
        const { initializeGlobalInputConfig, getGlobalInputConfig } = await import('./InputConfig.js');
        
        const config = initializeGlobalInputConfig();
        expect(config).toBeInstanceOf(InputConfig);
        
        const retrieved = getGlobalInputConfig();
        expect(retrieved).toBe(config);
    });
    
    test('应该能够获取现有的全局实例', async () => {
        const { getGlobalInputConfig } = await import('./InputConfig.js');
        
        const config1 = getGlobalInputConfig();
        const config2 = getGlobalInputConfig();
        
        expect(config1).toBe(config2);
    });
});
