/**
 * 输入系统单元测试
 * 测试InputSystem的ECS集成功能
 */

import { jest } from '@jest/globals';
import { inputSystem, InputEventSystem, createPlayerInputEntity } from './InputSystem.js';
import { InputEventTypes } from './InputManager.js';
import { Vector3 } from '@babylonjs/core/Maths/math.vector';

// Mock bitECS
const mockQuery = jest.fn();
jest.mock('bitecs', () => ({
    query: mockQuery
}));

// Mock ECS组件
const mockTransform = {
    x: [0], y: [0], z: [0],
    rotationX: [0], rotationY: [0], rotationZ: [0],
    scaleX: [1], scaleY: [1], scaleZ: [1]
};

const mockPhysics = {
    mass: [70],
    velocityX: [0], velocityY: [0], velocityZ: [0],
    friction: [0.8], restitution: [0.1],
    isKinematic: [0], isStatic: [0]
};

const mockPlayerInput = {
    moveForward: [0], moveBackward: [0],
    moveLeft: [0], moveRight: [0],
    run: [0], crouch: [0], jump: [0],
    lookX: [0], lookY: [0], zoom: [0],
    attack: [0], interact: [0], reload: [0],
    skill1: [0], skill2: [0], skill3: [0], skill4: [0], skill5: [0],
    skill6: [0], skill7: [0], skill8: [0], skill9: [0], skill0: [0],
    menu: [0], inventory: [0], map: [0], chat: [0]
};

jest.mock('./ecs/bitECSSystem.js', () => ({
    Transform: mockTransform,
    Physics: mockPhysics,
    PlayerInput: mockPlayerInput
}));

// Mock InputManager
const mockInputManager = {
    enabled: true,
    getMovementInput: jest.fn(() => Vector3.Zero()),
    getCameraInput: jest.fn(() => ({ rotation: { x: 0, y: 0 }, zoom: 0 })),
    isKeyPressed: jest.fn(() => false),
    isMouseButtonPressed: jest.fn(() => false),
    on: jest.fn(),
    off: jest.fn()
};

jest.mock('./InputManager.js', () => ({
    InputEventTypes: {
        MOVEMENT_START: 'movement:start',
        MOVEMENT_UPDATE: 'movement:update',
        MOVEMENT_STOP: 'movement:stop',
        ACTION_JUMP: 'action:jump',
        ACTION_ATTACK: 'action:attack',
        ACTION_INTERACT: 'action:interact',
        ACTION_RUN: 'action:run',
        CAMERA_ROTATE: 'camera:rotate',
        CAMERA_ZOOM: 'camera:zoom',
        UI_MENU: 'ui:menu',
        UI_INVENTORY: 'ui:inventory',
        UI_CHAT: 'ui:chat',
        UI_MAP: 'ui:map',
        SKILL_SLOT_1: 'skill:slot1',
        SKILL_SLOT_2: 'skill:slot2',
        SKILL_SLOT_3: 'skill:slot3',
        SKILL_SLOT_4: 'skill:slot4',
        SKILL_SLOT_5: 'skill:slot5',
        SKILL_SLOT_6: 'skill:slot6',
        SKILL_SLOT_7: 'skill:slot7',
        SKILL_SLOT_8: 'skill:slot8',
        SKILL_SLOT_9: 'skill:slot9',
        SKILL_SLOT_0: 'skill:slot0',
        SYSTEM_PAUSE: 'system:pause',
        SYSTEM_SCREENSHOT: 'system:screenshot'
    },
    getGlobalInputManager: jest.fn(() => mockInputManager)
}));

describe('inputSystem', () => {
    const mockWorld = {};
    const deltaTime = 0.016; // 60 FPS
    
    beforeEach(() => {
        jest.clearAllMocks();
        
        // 重置组件状态
        Object.keys(mockPlayerInput).forEach(key => {
            mockPlayerInput[key][0] = 0;
        });
        
        // 设置默认查询结果
        mockQuery.mockReturnValue([0]); // 实体ID 0
    });
    
    test('应该处理有PlayerInput组件的实体', () => {
        inputSystem(mockWorld, deltaTime);
        
        expect(mockQuery).toHaveBeenCalledWith(mockWorld, [mockPlayerInput]);
    });
    
    test('输入管理器禁用时应该跳过处理', () => {
        mockInputManager.enabled = false;
        
        inputSystem(mockWorld, deltaTime);
        
        expect(mockQuery).not.toHaveBeenCalled();
        
        // 恢复状态
        mockInputManager.enabled = true;
    });
    
    test('应该更新移动输入', () => {
        const movement = new Vector3(1, 0, 1); // 向前向右移动
        mockInputManager.getMovementInput.mockReturnValue(movement);
        
        inputSystem(mockWorld, deltaTime);
        
        expect(mockPlayerInput.moveForward[0]).toBe(1);
        expect(mockPlayerInput.moveRight[0]).toBe(1);
        expect(mockPlayerInput.moveBackward[0]).toBe(0);
        expect(mockPlayerInput.moveLeft[0]).toBe(0);
    });
    
    test('应该更新动作输入', () => {
        mockInputManager.isKeyPressed.mockImplementation((key) => {
            return key === 'shift'; // 只有shift键被按下
        });
        
        mockInputManager.isMouseButtonPressed.mockImplementation((button) => {
            return button === 0; // 左键被按下
        });
        
        inputSystem(mockWorld, deltaTime);
        
        expect(mockPlayerInput.run[0]).toBe(1);
        expect(mockPlayerInput.attack[0]).toBe(1);
        expect(mockPlayerInput.crouch[0]).toBe(0);
    });
    
    test('应该更新技能输入', () => {
        mockInputManager.isKeyPressed.mockImplementation((key) => {
            return ['1', '2', '3'].includes(key);
        });
        
        inputSystem(mockWorld, deltaTime);
        
        expect(mockPlayerInput.skill1[0]).toBe(1);
        expect(mockPlayerInput.skill2[0]).toBe(1);
        expect(mockPlayerInput.skill3[0]).toBe(1);
        expect(mockPlayerInput.skill4[0]).toBe(0);
    });
    
    test('应该更新相机输入', () => {
        const cameraInput = {
            rotation: { x: 10, y: 5 },
            zoom: 2
        };
        mockInputManager.getCameraInput.mockReturnValue(cameraInput);
        
        inputSystem(mockWorld, deltaTime);
        
        expect(mockPlayerInput.lookX[0]).toBe(10);
        expect(mockPlayerInput.lookY[0]).toBe(5);
        expect(mockPlayerInput.zoom[0]).toBe(2);
    });
});

describe('InputEventSystem', () => {
    let inputEventSystem;
    let mockECSWorld;
    
    beforeEach(() => {
        mockECSWorld = {
            getWorld: jest.fn(() => ({}))
        };
        
        inputEventSystem = new InputEventSystem(mockECSWorld);
        jest.clearAllMocks();
    });
    
    afterEach(() => {
        if (inputEventSystem) {
            inputEventSystem.destroy();
        }
    });
    
    describe('初始化', () => {
        test('应该正确初始化', () => {
            inputEventSystem.initialize(mockInputManager);
            
            expect(inputEventSystem.inputManager).toBe(mockInputManager);
            expect(mockInputManager.on).toHaveBeenCalledTimes(22); // 所有事件类型
        });
    });
    
    describe('事件处理', () => {
        beforeEach(() => {
            inputEventSystem.initialize(mockInputManager);
        });
        
        test('应该处理跳跃事件', () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
            mockQuery.mockReturnValue([0]);
            mockPhysics.velocityY[0] = 0; // 在地面上
            
            inputEventSystem.handleJump({ device: 'keyboard' });
            
            expect(mockPhysics.velocityY[0]).toBe(8.0);
            expect(consoleSpy).toHaveBeenCalledWith('🦘 跳跃:', { device: 'keyboard' });
            
            consoleSpy.mockRestore();
        });
        
        test('空中时不应该跳跃', () => {
            mockQuery.mockReturnValue([0]);
            mockPhysics.velocityY[0] = 5.0; // 在空中
            
            inputEventSystem.handleJump({ device: 'keyboard' });
            
            expect(mockPhysics.velocityY[0]).toBe(5.0); // 不应该改变
        });
        
        test('应该处理攻击事件', () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
            
            inputEventSystem.handleAttack({ device: 'mouse' });
            
            expect(consoleSpy).toHaveBeenCalledWith('⚔️ 攻击:', { device: 'mouse' });
            consoleSpy.mockRestore();
        });
        
        test('应该处理UI事件', () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
            
            inputEventSystem.handleUIMenu({ device: 'keyboard' });
            inputEventSystem.handleUIInventory({ device: 'keyboard' });
            
            expect(consoleSpy).toHaveBeenCalledWith('📋 菜单:', { device: 'keyboard' });
            expect(consoleSpy).toHaveBeenCalledWith('🎒 背包:', { device: 'keyboard' });
            
            consoleSpy.mockRestore();
        });
        
        test('应该处理技能事件', () => {
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
            
            inputEventSystem.handleSkillSlot(1, { device: 'keyboard' });
            inputEventSystem.handleSkillSlot(5, { device: 'keyboard' });
            
            expect(consoleSpy).toHaveBeenCalledWith('🔮 技能槽 1:', { device: 'keyboard' });
            expect(consoleSpy).toHaveBeenCalledWith('🔮 技能槽 5:', { device: 'keyboard' });
            
            consoleSpy.mockRestore();
        });
    });
    
    describe('事件监听器管理', () => {
        beforeEach(() => {
            inputEventSystem.initialize(mockInputManager);
        });
        
        test('应该能够移除事件处理器', () => {
            inputEventSystem.removeEventHandler(InputEventTypes.ACTION_JUMP);
            
            expect(mockInputManager.off).toHaveBeenCalledWith(
                InputEventTypes.ACTION_JUMP,
                expect.any(Function)
            );
        });
    });
    
    describe('销毁', () => {
        test('应该正确销毁', () => {
            inputEventSystem.initialize(mockInputManager);
            
            inputEventSystem.destroy();
            
            expect(inputEventSystem.inputManager).toBeNull();
            expect(inputEventSystem.ecsWorld).toBeNull();
        });
    });
});

describe('createPlayerInputEntity', () => {
    let mockECSWorld;
    
    beforeEach(() => {
        mockECSWorld = {
            createEntity: jest.fn(() => 1),
            addComponent: jest.fn()
        };
    });
    
    test('应该创建带有所有必要组件的实体', () => {
        const entity = createPlayerInputEntity(mockECSWorld, 10, 5, 15);
        
        expect(mockECSWorld.createEntity).toHaveBeenCalled();
        expect(mockECSWorld.addComponent).toHaveBeenCalledTimes(3);
        
        // 检查Transform组件
        expect(mockECSWorld.addComponent).toHaveBeenCalledWith(
            1,
            mockTransform,
            expect.objectContaining({ x: 10, y: 5, z: 15 })
        );
        
        // 检查Physics组件
        expect(mockECSWorld.addComponent).toHaveBeenCalledWith(
            1,
            mockPhysics,
            expect.objectContaining({ mass: 70.0 })
        );
        
        // 检查PlayerInput组件
        expect(mockECSWorld.addComponent).toHaveBeenCalledWith(
            1,
            mockPlayerInput,
            expect.objectContaining({
                moveForward: 0,
                moveBackward: 0,
                jump: 0
            })
        );
        
        expect(entity).toBe(1);
    });
    
    test('应该使用默认位置', () => {
        createPlayerInputEntity(mockECSWorld);
        
        expect(mockECSWorld.addComponent).toHaveBeenCalledWith(
            1,
            mockTransform,
            expect.objectContaining({ x: 0, y: 0, z: 0 })
        );
    });
});
