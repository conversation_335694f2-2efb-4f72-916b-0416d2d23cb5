/**
 * 输入管理器单元测试
 * 测试InputManager的核心功能
 */

import { jest } from '@jest/globals';
import { InputManager, InputEventTypes, InputDeviceTypes } from './InputManager.js';
import { Vector3, Vector2 } from '@babylonjs/core/Maths/math.vector';

// Mock Babylon.js Scene
const mockScene = {
    onKeyboardObservable: {
        add: jest.fn()
    },
    onPointerObservable: {
        add: jest.fn()
    }
};

// Mock KeyboardEventTypes and PointerEventTypes
jest.mock('@babylonjs/core', () => ({
    KeyboardEventTypes: {
        KEYDOWN: 1,
        KEYUP: 2
    },
    PointerEventTypes: {
        POINTERDOWN: 1,
        POINTERUP: 2,
        POINTERMOVE: 3,
        POINTERWHEEL: 4
    }
}));

describe('InputManager', () => {
    let inputManager;
    
    beforeEach(() => {
        // 重置所有mock
        jest.clearAllMocks();
        
        // 创建输入管理器实例
        inputManager = new InputManager(mockScene, { debug: false });
    });
    
    afterEach(() => {
        if (inputManager) {
            inputManager.destroy();
        }
    });
    
    describe('构造函数', () => {
        test('应该正确初始化输入管理器', () => {
            expect(inputManager.scene).toBe(mockScene);
            expect(inputManager.initialized).toBe(false);
            expect(inputManager.enabled).toBe(true);
            expect(inputManager.state).toBeDefined();
            expect(inputManager.inputMappings).toBeInstanceOf(Map);
        });
        
        test('应该接受配置参数', () => {
            const config = { debug: true, mouseSensitivity: 2.0 };
            const manager = new InputManager(mockScene, config);
            
            expect(manager.config).toEqual(config);
            expect(manager.debugMode).toBe(true);
            
            manager.destroy();
        });
    });
    
    describe('初始化', () => {
        test('应该成功初始化', async () => {
            await inputManager.initialize();
            
            expect(inputManager.initialized).toBe(true);
            expect(mockScene.onKeyboardObservable.add).toHaveBeenCalled();
            expect(mockScene.onPointerObservable.add).toHaveBeenCalled();
        });
        
        test('不应该重复初始化', async () => {
            await inputManager.initialize();
            const firstCallCount = mockScene.onKeyboardObservable.add.mock.calls.length;
            
            await inputManager.initialize();
            const secondCallCount = mockScene.onKeyboardObservable.add.mock.calls.length;
            
            expect(secondCallCount).toBe(firstCallCount);
        });
    });
    
    describe('输入映射', () => {
        beforeEach(async () => {
            await inputManager.initialize();
        });
        
        test('应该设置默认输入映射', () => {
            expect(inputManager.inputMappings.size).toBeGreaterThan(0);
            expect(inputManager.inputMappings.has('w')).toBe(true);
            expect(inputManager.inputMappings.has(' ')).toBe(true);
            expect(inputManager.inputMappings.has('mouse:0')).toBe(true);
        });
        
        test('应该能够添加自定义映射', () => {
            inputManager.setInputMapping('t', InputEventTypes.UI_CHAT);
            
            const mappings = inputManager.inputMappings.get('t');
            expect(mappings).toBeDefined();
            expect(mappings[0].eventType).toBe(InputEventTypes.UI_CHAT);
        });
        
        test('应该能够移除映射', () => {
            inputManager.setInputMapping('t', InputEventTypes.UI_CHAT);
            expect(inputManager.inputMappings.has('t')).toBe(true);
            
            inputManager.removeInputMapping('t');
            expect(inputManager.inputMappings.has('t')).toBe(false);
        });
        
        test('应该能够清除所有映射', () => {
            const originalSize = inputManager.inputMappings.size;
            expect(originalSize).toBeGreaterThan(0);
            
            inputManager.clearInputMappings();
            expect(inputManager.inputMappings.size).toBe(0);
        });
        
        test('应该能够重置为默认映射', () => {
            inputManager.clearInputMappings();
            expect(inputManager.inputMappings.size).toBe(0);
            
            inputManager.resetToDefaultMappings();
            expect(inputManager.inputMappings.size).toBeGreaterThan(0);
        });
    });
    
    describe('键盘输入处理', () => {
        beforeEach(async () => {
            await inputManager.initialize();
        });
        
        test('应该正确处理按键按下', () => {
            const mockEvent = { key: 'w' };
            inputManager.processKeyboardInput('w', true, mockEvent);
            
            expect(inputManager.state.isKeyPressed('w')).toBe(true);
        });
        
        test('应该正确处理按键释放', () => {
            inputManager.processKeyboardInput('w', true, {});
            expect(inputManager.state.isKeyPressed('w')).toBe(true);
            
            inputManager.processKeyboardInput('w', false, {});
            expect(inputManager.state.isKeyPressed('w')).toBe(false);
        });
        
        test('应该触发移动事件', (done) => {
            inputManager.on(InputEventTypes.MOVEMENT_START, (data) => {
                expect(data.device).toBe(InputDeviceTypes.KEYBOARD);
                expect(data.movement).toBeInstanceOf(Vector3);
                done();
            });
            
            inputManager.processKeyboardInput('w', true, {});
        });
    });
    
    describe('鼠标输入处理', () => {
        beforeEach(async () => {
            await inputManager.initialize();
        });
        
        test('应该正确处理鼠标按钮', () => {
            const position = new Vector2(100, 100);
            const mockEvent = { button: 0, clientX: 100, clientY: 100 };
            
            inputManager.processMouseInput('down', 0, position, mockEvent);
            expect(inputManager.state.isMouseButtonPressed(0)).toBe(true);
            
            inputManager.processMouseInput('up', 0, position, mockEvent);
            expect(inputManager.state.isMouseButtonPressed(0)).toBe(false);
        });
        
        test('应该正确处理鼠标移动', () => {
            const position1 = new Vector2(100, 100);
            const position2 = new Vector2(150, 120);
            
            inputManager.state.updateMousePosition(position1);
            inputManager.state.updateMousePosition(position2);
            
            const delta = inputManager.getMouseDelta();
            expect(delta.x).toBe(50);
            expect(delta.y).toBe(20);
        });
        
        test('应该触发相机旋转事件', (done) => {
            inputManager.on(InputEventTypes.CAMERA_ROTATE, (data) => {
                expect(data.device).toBe(InputDeviceTypes.MOUSE);
                expect(data.rotation).toBeInstanceOf(Vector2);
                done();
            });
            
            const position = new Vector2(100, 100);
            inputManager.state.updateMousePosition(new Vector2(50, 50));
            inputManager.processMouseInput('move', -1, position, {});
        });
    });
    
    describe('状态管理', () => {
        beforeEach(async () => {
            await inputManager.initialize();
        });
        
        test('应该正确获取移动输入', () => {
            inputManager.processKeyboardInput('w', true, {});
            inputManager.processKeyboardInput('d', true, {});
            
            const movement = inputManager.getMovementInput();
            expect(movement.z).toBeCloseTo(1);
            expect(movement.x).toBeCloseTo(1);
        });
        
        test('应该正确标准化移动向量', () => {
            inputManager.processKeyboardInput('w', true, {});
            inputManager.processKeyboardInput('d', true, {});
            
            const movement = inputManager.getMovementInput();
            const length = movement.length();
            expect(length).toBeCloseTo(1, 5);
        });
        
        test('应该正确获取相机输入', () => {
            const rotation = new Vector2(10, 5);
            const zoom = 2;
            
            inputManager.state.updateCameraInput(rotation, zoom);
            
            const cameraInput = inputManager.getCameraInput();
            expect(cameraInput.rotation.x).toBe(10);
            expect(cameraInput.rotation.y).toBe(5);
            expect(cameraInput.zoom).toBe(2);
        });
    });
    
    describe('启用/禁用', () => {
        beforeEach(async () => {
            await inputManager.initialize();
        });
        
        test('应该能够禁用输入管理器', () => {
            expect(inputManager.enabled).toBe(true);
            
            inputManager.disable();
            expect(inputManager.enabled).toBe(false);
        });
        
        test('应该能够启用输入管理器', () => {
            inputManager.disable();
            expect(inputManager.enabled).toBe(false);
            
            inputManager.enable();
            expect(inputManager.enabled).toBe(true);
        });
        
        test('禁用时应该重置状态', () => {
            inputManager.processKeyboardInput('w', true, {});
            expect(inputManager.state.isKeyPressed('w')).toBe(true);
            
            inputManager.disable();
            expect(inputManager.state.isKeyPressed('w')).toBe(false);
        });
    });
    
    describe('统计信息', () => {
        beforeEach(async () => {
            await inputManager.initialize();
        });
        
        test('应该返回正确的统计信息', () => {
            inputManager.processKeyboardInput('w', true, {});
            inputManager.state.setMouseButtonState(0, true);
            
            const stats = inputManager.getStats();
            
            expect(stats.initialized).toBe(true);
            expect(stats.enabled).toBe(true);
            expect(stats.mappingCount).toBeGreaterThan(0);
            expect(stats.keyboardKeysPressed).toContain('w');
            expect(stats.mouseButtonsPressed).toContain(0);
        });
    });
    
    describe('销毁', () => {
        test('应该正确销毁输入管理器', async () => {
            await inputManager.initialize();
            expect(inputManager.initialized).toBe(true);
            
            inputManager.destroy();
            
            expect(inputManager.initialized).toBe(false);
            expect(inputManager.enabled).toBe(false);
            expect(inputManager.inputMappings.size).toBe(0);
        });
    });
});

describe('全局输入管理器', () => {
    test('应该能够初始化全局实例', async () => {
        const { initializeGlobalInputManager, getGlobalInputManager, destroyGlobalInputManager } = await import('./InputManager.js');
        
        const manager = await initializeGlobalInputManager(mockScene);
        expect(manager).toBeInstanceOf(InputManager);
        
        const retrieved = getGlobalInputManager();
        expect(retrieved).toBe(manager);
        
        destroyGlobalInputManager();
        expect(getGlobalInputManager()).toBeNull();
    });
});
