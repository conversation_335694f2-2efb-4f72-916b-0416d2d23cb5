/**
 * 输入系统 (InputSystem)
 * 将输入管理器与bitECS系统集成
 * 处理PlayerInput组件的更新和输入事件的ECS分发
 */

import { query } from 'bitecs';
import { Transform, Physics, PlayerInput } from './ecs/bitECSSystem.js';
import { InputEventTypes, getGlobalInputManager } from './InputManager.js';
import { getGlobalInputConfig } from './InputConfig.js';
import { Vector3 } from '@babylonjs/core/Maths/math.vector';

/**
 * 输入系统主函数
 * 处理所有具有PlayerInput组件的实体
 * @param {Object} world - bitECS世界实例
 * @param {number} deltaTime - 时间增量（秒）
 */
export function inputSystem(world, deltaTime) {
    const inputManager = getGlobalInputManager();
    if (!inputManager || !inputManager.enabled) return;
    
    // 查询所有具有PlayerInput组件的实体
    const inputEntities = query(world, [PlayerInput]);
    
    for (const entity of inputEntities) {
        // 更新实体的输入状态
        updateEntityInput(entity, inputManager, deltaTime);
    }
}

/**
 * 更新实体的输入状态
 * @param {number} entity - 实体ID
 * @param {InputManager} inputManager - 输入管理器
 * @param {number} deltaTime - 时间增量
 */
function updateEntityInput(entity, inputManager, deltaTime) {
    // 获取移动输入
    const movementInput = inputManager.getMovementInput();
    
    // 更新PlayerInput组件的移动状态
    PlayerInput.moveForward[entity] = movementInput.z > 0.1 ? 1 : 0;
    PlayerInput.moveBackward[entity] = movementInput.z < -0.1 ? 1 : 0;
    PlayerInput.moveLeft[entity] = movementInput.x < -0.1 ? 1 : 0;
    PlayerInput.moveRight[entity] = movementInput.x > 0.1 ? 1 : 0;
    
    // 更新其他输入状态
    PlayerInput.run[entity] = inputManager.isKeyPressed('shift') ? 1 : 0;
    PlayerInput.crouch[entity] = inputManager.isKeyPressed('ctrl') ? 1 : 0;
    
    // 跳跃输入需要特殊处理（一次性触发）
    if (inputManager.isKeyPressed(' ') && PlayerInput.jump[entity] === 0) {
        PlayerInput.jump[entity] = 1;
    } else if (!inputManager.isKeyPressed(' ')) {
        PlayerInput.jump[entity] = 0;
    }
    
    // 获取相机输入
    const cameraInput = inputManager.getCameraInput();
    PlayerInput.lookX[entity] = cameraInput.rotation.x;
    PlayerInput.lookY[entity] = cameraInput.rotation.y;
    PlayerInput.zoom[entity] = cameraInput.zoom;
    
    // 更新动作输入
    PlayerInput.attack[entity] = inputManager.isMouseButtonPressed(0) ? 1 : 0;
    PlayerInput.interact[entity] = inputManager.isKeyPressed('e') ? 1 : 0;
    PlayerInput.reload[entity] = inputManager.isKeyPressed('r') ? 1 : 0;
    
    // 更新技能输入
    PlayerInput.skill1[entity] = inputManager.isKeyPressed('1') ? 1 : 0;
    PlayerInput.skill2[entity] = inputManager.isKeyPressed('2') ? 1 : 0;
    PlayerInput.skill3[entity] = inputManager.isKeyPressed('3') ? 1 : 0;
    PlayerInput.skill4[entity] = inputManager.isKeyPressed('4') ? 1 : 0;
    PlayerInput.skill5[entity] = inputManager.isKeyPressed('5') ? 1 : 0;
    PlayerInput.skill6[entity] = inputManager.isKeyPressed('6') ? 1 : 0;
    PlayerInput.skill7[entity] = inputManager.isKeyPressed('7') ? 1 : 0;
    PlayerInput.skill8[entity] = inputManager.isKeyPressed('8') ? 1 : 0;
    PlayerInput.skill9[entity] = inputManager.isKeyPressed('9') ? 1 : 0;
    PlayerInput.skill0[entity] = inputManager.isKeyPressed('0') ? 1 : 0;
    
    // 更新UI输入
    PlayerInput.menu[entity] = inputManager.isKeyPressed('escape') ? 1 : 0;
    PlayerInput.inventory[entity] = inputManager.isKeyPressed('i') ? 1 : 0;
    PlayerInput.map[entity] = inputManager.isKeyPressed('m') ? 1 : 0;
    PlayerInput.chat[entity] = inputManager.isKeyPressed('enter') ? 1 : 0;
}

/**
 * 输入事件处理系统
 * 监听输入管理器的事件并转换为ECS事件
 */
export class InputEventSystem {
    constructor(ecsWorld) {
        this.ecsWorld = ecsWorld;
        this.inputManager = null;
        this.eventListeners = new Map();
        
        console.log('🎮 输入事件系统已创建');
    }
    
    /**
     * 初始化输入事件系统
     * @param {InputManager} inputManager - 输入管理器
     */
    initialize(inputManager) {
        this.inputManager = inputManager;
        this.setupEventListeners();
        console.log('✅ 输入事件系统已初始化');
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        if (!this.inputManager) return;
        
        // 移动事件
        this.addEventHandler(InputEventTypes.MOVEMENT_START, this.handleMovementStart.bind(this));
        this.addEventHandler(InputEventTypes.MOVEMENT_UPDATE, this.handleMovementUpdate.bind(this));
        this.addEventHandler(InputEventTypes.MOVEMENT_STOP, this.handleMovementStop.bind(this));
        
        // 动作事件
        this.addEventHandler(InputEventTypes.ACTION_JUMP, this.handleJump.bind(this));
        this.addEventHandler(InputEventTypes.ACTION_ATTACK, this.handleAttack.bind(this));
        this.addEventHandler(InputEventTypes.ACTION_INTERACT, this.handleInteract.bind(this));
        this.addEventHandler(InputEventTypes.ACTION_RUN, this.handleRun.bind(this));
        
        // 相机事件
        this.addEventHandler(InputEventTypes.CAMERA_ROTATE, this.handleCameraRotate.bind(this));
        this.addEventHandler(InputEventTypes.CAMERA_ZOOM, this.handleCameraZoom.bind(this));
        
        // UI事件
        this.addEventHandler(InputEventTypes.UI_MENU, this.handleUIMenu.bind(this));
        this.addEventHandler(InputEventTypes.UI_INVENTORY, this.handleUIInventory.bind(this));
        this.addEventHandler(InputEventTypes.UI_CHAT, this.handleUIChat.bind(this));
        this.addEventHandler(InputEventTypes.UI_MAP, this.handleUIMap.bind(this));
        
        // 技能事件
        this.addEventHandler(InputEventTypes.SKILL_SLOT_1, this.handleSkillSlot.bind(this, 1));
        this.addEventHandler(InputEventTypes.SKILL_SLOT_2, this.handleSkillSlot.bind(this, 2));
        this.addEventHandler(InputEventTypes.SKILL_SLOT_3, this.handleSkillSlot.bind(this, 3));
        this.addEventHandler(InputEventTypes.SKILL_SLOT_4, this.handleSkillSlot.bind(this, 4));
        this.addEventHandler(InputEventTypes.SKILL_SLOT_5, this.handleSkillSlot.bind(this, 5));
        this.addEventHandler(InputEventTypes.SKILL_SLOT_6, this.handleSkillSlot.bind(this, 6));
        this.addEventHandler(InputEventTypes.SKILL_SLOT_7, this.handleSkillSlot.bind(this, 7));
        this.addEventHandler(InputEventTypes.SKILL_SLOT_8, this.handleSkillSlot.bind(this, 8));
        this.addEventHandler(InputEventTypes.SKILL_SLOT_9, this.handleSkillSlot.bind(this, 9));
        this.addEventHandler(InputEventTypes.SKILL_SLOT_0, this.handleSkillSlot.bind(this, 0));
        
        // 系统事件
        this.addEventHandler(InputEventTypes.SYSTEM_PAUSE, this.handleSystemPause.bind(this));
        this.addEventHandler(InputEventTypes.SYSTEM_SCREENSHOT, this.handleSystemScreenshot.bind(this));
    }
    
    /**
     * 添加事件处理器
     * @param {string} eventType - 事件类型
     * @param {Function} handler - 处理函数
     */
    addEventHandler(eventType, handler) {
        this.inputManager.on(eventType, handler);
        this.eventListeners.set(eventType, handler);
    }
    
    /**
     * 移除事件处理器
     * @param {string} eventType - 事件类型
     */
    removeEventHandler(eventType) {
        const handler = this.eventListeners.get(eventType);
        if (handler) {
            this.inputManager.off(eventType, handler);
            this.eventListeners.delete(eventType);
        }
    }
    
    // 事件处理方法
    
    handleMovementStart(data) {
        console.log('🚶 移动开始:', data);
        // 可以在这里触发移动动画等
    }
    
    handleMovementUpdate(data) {
        // 移动更新在inputSystem中处理，这里可以处理额外逻辑
    }
    
    handleMovementStop(data) {
        console.log('🛑 移动停止:', data);
        // 可以在这里停止移动动画等
    }
    
    handleJump(data) {
        console.log('🦘 跳跃:', data);
        // 触发跳跃逻辑
        const playerEntities = query(this.ecsWorld.getWorld(), [PlayerInput, Physics]);
        for (const entity of playerEntities) {
            // 检查是否在地面上
            if (Math.abs(Physics.velocityY[entity]) < 0.1) {
                const jumpForce = 8.0;
                Physics.velocityY[entity] = jumpForce;
                console.log(`实体 ${entity} 执行跳跃`);
            }
        }
    }
    
    handleAttack(data) {
        console.log('⚔️ 攻击:', data);
        // 触发攻击逻辑
    }
    
    handleInteract(data) {
        console.log('🤝 交互:', data);
        // 触发交互逻辑
    }
    
    handleRun(data) {
        console.log('🏃 奔跑:', data);
        // 触发奔跑状态切换
    }
    
    handleCameraRotate(data) {
        // 相机旋转在相机系统中处理
    }
    
    handleCameraZoom(data) {
        // 相机缩放在相机系统中处理
    }
    
    handleUIMenu(data) {
        console.log('📋 菜单:', data);
        // 触发菜单显示/隐藏
    }
    
    handleUIInventory(data) {
        console.log('🎒 背包:', data);
        // 触发背包显示/隐藏
    }
    
    handleUIChat(data) {
        console.log('💬 聊天:', data);
        // 触发聊天窗口
    }
    
    handleUIMap(data) {
        console.log('🗺️ 地图:', data);
        // 触发地图显示/隐藏
    }
    
    handleSkillSlot(slot, data) {
        console.log(`🔮 技能槽 ${slot}:`, data);
        // 触发技能使用
    }
    
    handleSystemPause(data) {
        console.log('⏸️ 暂停:', data);
        // 触发游戏暂停
    }
    
    handleSystemScreenshot(data) {
        console.log('📸 截图:', data);
        // 触发截图功能
    }
    
    /**
     * 销毁输入事件系统
     */
    destroy() {
        // 移除所有事件监听器
        for (const eventType of this.eventListeners.keys()) {
            this.removeEventHandler(eventType);
        }
        
        this.inputManager = null;
        this.ecsWorld = null;
        
        console.log('💥 输入事件系统已销毁');
    }
}

/**
 * 创建玩家输入实体的辅助函数
 * @param {Object} ecsWorld - ECS世界实例
 * @param {number} x - 初始X位置
 * @param {number} y - 初始Y位置
 * @param {number} z - 初始Z位置
 * @returns {number} 实体ID
 */
export function createPlayerInputEntity(ecsWorld, x = 0, y = 0, z = 0) {
    const entity = ecsWorld.createEntity();
    
    // 添加Transform组件
    ecsWorld.addComponent(entity, Transform, {
        x: x, y: y, z: z,
        rotationX: 0, rotationY: 0, rotationZ: 0,
        scaleX: 1, scaleY: 1, scaleZ: 1
    });
    
    // 添加Physics组件
    ecsWorld.addComponent(entity, Physics, {
        mass: 70.0,
        velocityX: 0, velocityY: 0, velocityZ: 0,
        friction: 0.8,
        restitution: 0.1,
        isKinematic: 0,
        isStatic: 0
    });
    
    // 添加PlayerInput组件
    ecsWorld.addComponent(entity, PlayerInput, {
        moveForward: 0, moveBackward: 0,
        moveLeft: 0, moveRight: 0,
        run: 0, crouch: 0, jump: 0,
        lookX: 0, lookY: 0, zoom: 0,
        attack: 0, interact: 0, reload: 0,
        skill1: 0, skill2: 0, skill3: 0, skill4: 0, skill5: 0,
        skill6: 0, skill7: 0, skill8: 0, skill9: 0, skill0: 0,
        menu: 0, inventory: 0, map: 0, chat: 0
    });
    
    console.log(`🎮 玩家输入实体已创建: ${entity}`);
    return entity;
}

console.log('🎮 输入系统模块已加载');
