/**
 * 输入配置管理器 (InputConfig)
 * 为Cantos MMORPG提供输入配置的管理和持久化
 * 支持键位映射、敏感度设置、设备配置等
 */

import { InputEventTypes } from './InputEventTypes.js';

/**
 * 默认输入配置
 */
export const DefaultInputConfig = {
    // 键盘映射配置
    keyboardMappings: {
        // 移动控制
        'w': { eventType: InputEventTypes.MOVEMENT_UPDATE, data: { direction: 'forward' } },
        's': { eventType: InputEventTypes.MOVEMENT_UPDATE, data: { direction: 'backward' } },
        'a': { eventType: InputEventTypes.MOVEMENT_UPDATE, data: { direction: 'left' } },
        'd': { eventType: InputEventTypes.MOVEMENT_UPDATE, data: { direction: 'right' } },
        'arrowup': { eventType: InputEventTypes.MOVEMENT_UPDATE, data: { direction: 'forward' } },
        'arrowdown': { eventType: InputEventTypes.MOVEMENT_UPDATE, data: { direction: 'backward' } },
        'arrowleft': { eventType: InputEventTypes.MOVEMENT_UPDATE, data: { direction: 'left' } },
        'arrowright': { eventType: InputEventTypes.MOVEMENT_UPDATE, data: { direction: 'right' } },

        // 动作控制
        ' ': { eventType: InputEventTypes.ACTION_JUMP, data: {} },
        'shift': { eventType: InputEventTypes.ACTION_RUN, data: {} },
        'e': { eventType: InputEventTypes.ACTION_INTERACT, data: {} },
        'f': { eventType: InputEventTypes.ACTION_ATTACK, data: {} },

        // UI控制
        'escape': { eventType: InputEventTypes.UI_MENU, data: {} },
        'i': { eventType: InputEventTypes.UI_INVENTORY, data: {} },
        'enter': { eventType: InputEventTypes.UI_CHAT, data: {} },
        'm': { eventType: InputEventTypes.UI_MAP, data: {} },

        // 技能快捷键
        '1': { eventType: InputEventTypes.SKILL_SLOT_1, data: { slot: 1 } },
        '2': { eventType: InputEventTypes.SKILL_SLOT_2, data: { slot: 2 } },
        '3': { eventType: InputEventTypes.SKILL_SLOT_3, data: { slot: 3 } },
        '4': { eventType: InputEventTypes.SKILL_SLOT_4, data: { slot: 4 } },
        '5': { eventType: InputEventTypes.SKILL_SLOT_5, data: { slot: 5 } },
        '6': { eventType: InputEventTypes.SKILL_SLOT_6, data: { slot: 6 } },
        '7': { eventType: InputEventTypes.SKILL_SLOT_7, data: { slot: 7 } },
        '8': { eventType: InputEventTypes.SKILL_SLOT_8, data: { slot: 8 } },
        '9': { eventType: InputEventTypes.SKILL_SLOT_9, data: { slot: 9 } },
        '0': { eventType: InputEventTypes.SKILL_SLOT_0, data: { slot: 0 } },

        // 系统控制
        'p': { eventType: InputEventTypes.SYSTEM_PAUSE, data: {} },
        'f12': { eventType: InputEventTypes.SYSTEM_SCREENSHOT, data: {} }
    },

    // 鼠标映射配置
    mouseMappings: {
        'mouse:0': { eventType: InputEventTypes.ACTION_ATTACK, data: { button: 'left' } },
        'mouse:2': { eventType: InputEventTypes.ACTION_INTERACT, data: { button: 'right' } },
        'mouse:move': { eventType: InputEventTypes.CAMERA_ROTATE, data: {} },
        'mouse:wheel': { eventType: InputEventTypes.CAMERA_ZOOM, data: {} }
    },

    // 触摸映射配置
    touchMappings: {
        'touch:tap': { eventType: InputEventTypes.ACTION_INTERACT, data: {} },
        'touch:drag': { eventType: InputEventTypes.CAMERA_ROTATE, data: {} },
        'touch:pinch': { eventType: InputEventTypes.CAMERA_ZOOM, data: {} }
    },

    // 手柄映射配置
    gamepadMappings: {
        'gamepad:a': { eventType: InputEventTypes.ACTION_JUMP, data: {} },
        'gamepad:b': { eventType: InputEventTypes.ACTION_ATTACK, data: {} },
        'gamepad:x': { eventType: InputEventTypes.ACTION_INTERACT, data: {} },
        'gamepad:y': { eventType: InputEventTypes.UI_INVENTORY, data: {} },
        'gamepad:leftstick': { eventType: InputEventTypes.MOVEMENT_UPDATE, data: {} },
        'gamepad:rightstick': { eventType: InputEventTypes.CAMERA_ROTATE, data: {} }
    },

    // 敏感度设置
    sensitivity: {
        mouseSensitivity: 1.0,          // 鼠标灵敏度
        zoomSensitivity: 1.0,           // 缩放灵敏度
        touchSensitivity: 1.0,          // 触摸灵敏度
        gamepadSensitivity: 1.0,        // 手柄灵敏度
        cameraRotationSpeed: 2.0,       // 相机旋转速度
        movementSpeed: 5.0,             // 移动速度
        runSpeedMultiplier: 2.0         // 跑步速度倍数
    },

    // 设备设置
    devices: {
        keyboard: {
            enabled: true,
            repeatDelay: 500,           // 按键重复延迟 (毫秒)
            repeatRate: 50              // 按键重复频率 (毫秒)
        },
        mouse: {
            enabled: true,
            invertY: false,             // 是否反转Y轴
            smoothing: true,            // 是否启用平滑
            smoothingFactor: 0.1        // 平滑系数
        },
        touch: {
            enabled: true,
            multiTouch: true,           // 是否支持多点触控
            gestureThreshold: 10        // 手势识别阈值 (像素)
        },
        gamepad: {
            enabled: true,
            deadZone: 0.1,              // 摇杆死区
            vibration: true             // 是否启用震动
        }
    },

    // 高级设置
    advanced: {
        debugMode: false,               // 调试模式
        logInputEvents: false,          // 记录输入事件
        preventDefaultEvents: true,     // 阻止默认事件
        captureAllKeys: false,          // 捕获所有按键
        enableContextMenu: false        // 启用右键菜单
    }
};

/**
 * 输入配置管理器类
 * 管理输入配置的加载、保存和应用
 */
export class InputConfig {
    constructor(config = {}) {
        // 合并默认配置和用户配置
        this.config = this.mergeConfigs(DefaultInputConfig, config);

        // 配置存储键名
        this.storageKey = 'cantos_input_config';

        // 配置变更监听器
        this.changeListeners = new Set();

        console.log('⚙️ 输入配置管理器已创建');
    }

    /**
     * 深度合并配置对象
     * @param {Object} defaultConfig - 默认配置
     * @param {Object} userConfig - 用户配置
     * @returns {Object} 合并后的配置
     */
    mergeConfigs(defaultConfig, userConfig) {
        const merged = JSON.parse(JSON.stringify(defaultConfig));

        for (const key in userConfig) {
            if (userConfig.hasOwnProperty(key)) {
                if (typeof userConfig[key] === 'object' && userConfig[key] !== null && !Array.isArray(userConfig[key])) {
                    merged[key] = this.mergeConfigs(merged[key] || {}, userConfig[key]);
                } else {
                    merged[key] = userConfig[key];
                }
            }
        }

        return merged;
    }

    /**
     * 从本地存储加载配置
     * @returns {boolean} 是否成功加载
     */
    loadFromStorage() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const storedConfig = JSON.parse(stored);
                this.config = this.mergeConfigs(DefaultInputConfig, storedConfig);
                this.notifyChange('loaded');
                console.log('📁 输入配置已从本地存储加载');
                return true;
            }
        } catch (error) {
            console.error('❌ 加载输入配置失败:', error);
        }
        return false;
    }

    /**
     * 保存配置到本地存储
     * @returns {boolean} 是否成功保存
     */
    saveToStorage() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.config));
            this.notifyChange('saved');
            console.log('💾 输入配置已保存到本地存储');
            return true;
        } catch (error) {
            console.error('❌ 保存输入配置失败:', error);
            return false;
        }
    }

    /**
     * 重置为默认配置
     */
    resetToDefault() {
        this.config = JSON.parse(JSON.stringify(DefaultInputConfig));
        this.notifyChange('reset');
        console.log('🔄 输入配置已重置为默认');
    }

    /**
     * 获取完整配置
     * @returns {Object} 配置对象
     */
    getConfig() {
        return JSON.parse(JSON.stringify(this.config));
    }

    /**
     * 获取键盘映射
     * @returns {Object} 键盘映射配置
     */
    getKeyboardMappings() {
        return { ...this.config.keyboardMappings };
    }

    /**
     * 获取鼠标映射
     * @returns {Object} 鼠标映射配置
     */
    getMouseMappings() {
        return { ...this.config.mouseMappings };
    }

    /**
     * 获取触摸映射
     * @returns {Object} 触摸映射配置
     */
    getTouchMappings() {
        return { ...this.config.touchMappings };
    }

    /**
     * 获取手柄映射
     * @returns {Object} 手柄映射配置
     */
    getGamepadMappings() {
        return { ...this.config.gamepadMappings };
    }

    /**
     * 获取敏感度设置
     * @returns {Object} 敏感度配置
     */
    getSensitivity() {
        return { ...this.config.sensitivity };
    }

    /**
     * 获取设备设置
     * @returns {Object} 设备配置
     */
    getDevices() {
        return JSON.parse(JSON.stringify(this.config.devices));
    }

    /**
     * 获取高级设置
     * @returns {Object} 高级配置
     */
    getAdvanced() {
        return { ...this.config.advanced };
    }

    /**
     * 设置键盘映射
     * @param {string} key - 按键
     * @param {string} eventType - 事件类型
     * @param {Object} data - 附加数据
     */
    setKeyboardMapping(key, eventType, data = {}) {
        this.config.keyboardMappings[key.toLowerCase()] = { eventType, data };
        this.notifyChange('keyboardMapping', { key, eventType, data });
    }

    /**
     * 移除键盘映射
     * @param {string} key - 按键
     */
    removeKeyboardMapping(key) {
        delete this.config.keyboardMappings[key.toLowerCase()];
        this.notifyChange('keyboardMapping', { key, removed: true });
    }

    /**
     * 设置鼠标映射
     * @param {string} input - 鼠标输入标识符
     * @param {string} eventType - 事件类型
     * @param {Object} data - 附加数据
     */
    setMouseMapping(input, eventType, data = {}) {
        this.config.mouseMappings[input] = { eventType, data };
        this.notifyChange('mouseMapping', { input, eventType, data });
    }

    /**
     * 移除鼠标映射
     * @param {string} input - 鼠标输入标识符
     */
    removeMouseMapping(input) {
        delete this.config.mouseMappings[input];
        this.notifyChange('mouseMapping', { input, removed: true });
    }

    /**
     * 设置敏感度
     * @param {string} type - 敏感度类型
     * @param {number} value - 敏感度值
     */
    setSensitivity(type, value) {
        if (this.config.sensitivity.hasOwnProperty(type)) {
            this.config.sensitivity[type] = Math.max(0.1, Math.min(5.0, value));
            this.notifyChange('sensitivity', { type, value: this.config.sensitivity[type] });
        }
    }

    /**
     * 设置设备配置
     * @param {string} device - 设备类型
     * @param {string} setting - 设置名称
     * @param {*} value - 设置值
     */
    setDeviceSetting(device, setting, value) {
        if (this.config.devices[device] && this.config.devices[device].hasOwnProperty(setting)) {
            this.config.devices[device][setting] = value;
            this.notifyChange('deviceSetting', { device, setting, value });
        }
    }

    /**
     * 设置高级配置
     * @param {string} setting - 设置名称
     * @param {*} value - 设置值
     */
    setAdvancedSetting(setting, value) {
        if (this.config.advanced.hasOwnProperty(setting)) {
            this.config.advanced[setting] = value;
            this.notifyChange('advancedSetting', { setting, value });
        }
    }

    /**
     * 验证配置有效性
     * @returns {Object} 验证结果
     */
    validateConfig() {
        const errors = [];
        const warnings = [];

        // 检查必要的映射
        const requiredMappings = [
            InputEventTypes.MOVEMENT_UPDATE,
            InputEventTypes.ACTION_JUMP,
            InputEventTypes.ACTION_ATTACK
        ];

        for (const eventType of requiredMappings) {
            const hasMapping = Object.values(this.config.keyboardMappings)
                .some(mapping => mapping.eventType === eventType) ||
                Object.values(this.config.mouseMappings)
                .some(mapping => mapping.eventType === eventType);

            if (!hasMapping) {
                warnings.push(`缺少 ${eventType} 的输入映射`);
            }
        }

        // 检查敏感度范围
        for (const [key, value] of Object.entries(this.config.sensitivity)) {
            if (typeof value !== 'number' || value < 0.1 || value > 5.0) {
                errors.push(`敏感度 ${key} 值无效: ${value}`);
            }
        }

        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 导出配置为JSON
     * @returns {string} JSON字符串
     */
    exportToJSON() {
        return JSON.stringify(this.config, null, 2);
    }

    /**
     * 从JSON导入配置
     * @param {string} jsonString - JSON字符串
     * @returns {boolean} 是否成功导入
     */
    importFromJSON(jsonString) {
        try {
            const importedConfig = JSON.parse(jsonString);
            this.config = this.mergeConfigs(DefaultInputConfig, importedConfig);
            this.notifyChange('imported');
            console.log('📥 输入配置已从JSON导入');
            return true;
        } catch (error) {
            console.error('❌ 导入输入配置失败:', error);
            return false;
        }
    }

    /**
     * 添加配置变更监听器
     * @param {Function} listener - 监听器函数
     */
    addChangeListener(listener) {
        this.changeListeners.add(listener);
    }

    /**
     * 移除配置变更监听器
     * @param {Function} listener - 监听器函数
     */
    removeChangeListener(listener) {
        this.changeListeners.delete(listener);
    }

    /**
     * 通知配置变更
     * @param {string} type - 变更类型
     * @param {*} data - 变更数据
     */
    notifyChange(type, data = null) {
        for (const listener of this.changeListeners) {
            try {
                listener(type, data, this.config);
            } catch (error) {
                console.error('配置变更监听器执行错误:', error);
            }
        }
    }

    /**
     * 获取配置统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            keyboardMappings: Object.keys(this.config.keyboardMappings).length,
            mouseMappings: Object.keys(this.config.mouseMappings).length,
            touchMappings: Object.keys(this.config.touchMappings).length,
            gamepadMappings: Object.keys(this.config.gamepadMappings).length,
            enabledDevices: Object.entries(this.config.devices)
                .filter(([device, settings]) => settings.enabled)
                .map(([device]) => device),
            debugMode: this.config.advanced.debugMode
        };
    }
}

// 创建全局配置实例
let globalInputConfig = null;

/**
 * 获取全局输入配置实例
 * @returns {InputConfig} 配置实例
 */
export function getGlobalInputConfig() {
    if (!globalInputConfig) {
        globalInputConfig = new InputConfig();
        globalInputConfig.loadFromStorage();
    }
    return globalInputConfig;
}

/**
 * 初始化全局输入配置
 * @param {Object} config - 初始配置
 * @returns {InputConfig} 配置实例
 */
export function initializeGlobalInputConfig(config = {}) {
    globalInputConfig = new InputConfig(config);
    globalInputConfig.loadFromStorage();

    // 暴露到全局作用域以便调试
    if (typeof window !== 'undefined') {
        window.inputConfig = globalInputConfig;
    }

    console.log('🌍 全局输入配置已初始化');
    return globalInputConfig;
}

console.log('⚙️ 输入配置模块已加载');
