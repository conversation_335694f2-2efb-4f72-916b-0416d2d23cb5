/**
 * 输入管理器 (InputManager)
 * 为Cantos MMORPG提供统一的输入处理系统
 * 支持键盘、鼠标、触摸和手柄输入，集成ECS架构
 */

import { Vector3, Vector2 } from '@babylonjs/core/Maths/math.vector';
import { KeyboardEventTypes, PointerEventTypes } from '@babylonjs/core';
import { EventEmitter } from './eventEmitter/index.js';

/**
 * 输入事件类型定义
 */
export const InputEventTypes = {
    // 移动相关
    MOVEMENT_START: 'movement:start',
    MOVEMENT_UPDATE: 'movement:update',
    MOVEMENT_STOP: 'movement:stop',

    // 动作相关
    ACTION_JUMP: 'action:jump',
    ACTION_ATTACK: 'action:attack',
    ACTION_INTERACT: 'action:interact',
    ACTION_RUN: 'action:run',

    // 相机相关
    CAMERA_ROTATE: 'camera:rotate',
    CAMERA_ZOOM: 'camera:zoom',

    // UI相关
    UI_MENU: 'ui:menu',
    UI_INVENTORY: 'ui:inventory',
    UI_CHAT: 'ui:chat',
    UI_MAP: 'ui:map',

    // 技能相关
    SKILL_SLOT_1: 'skill:slot1',
    SKILL_SLOT_2: 'skill:slot2',
    SKILL_SLOT_3: 'skill:slot3',
    SKILL_SLOT_4: 'skill:slot4',
    SKILL_SLOT_5: 'skill:slot5',
    SKILL_SLOT_6: 'skill:slot6',
    SKILL_SLOT_7: 'skill:slot7',
    SKILL_SLOT_8: 'skill:slot8',
    SKILL_SLOT_9: 'skill:slot9',
    SKILL_SLOT_0: 'skill:slot0',

    // 系统相关
    SYSTEM_PAUSE: 'system:pause',
    SYSTEM_SCREENSHOT: 'system:screenshot'
};

/**
 * 输入设备类型
 */
export const InputDeviceTypes = {
    KEYBOARD: 'keyboard',
    MOUSE: 'mouse',
    TOUCH: 'touch',
    GAMEPAD: 'gamepad'
};

/**
 * 输入状态管理器
 * 统一管理所有输入设备的状态
 */
class InputStateManager {
    constructor() {
        // 键盘状态
        this.keyboardState = new Map();

        // 鼠标状态
        this.mouseState = {
            position: Vector2.Zero(),
            deltaPosition: Vector2.Zero(),
            buttons: new Map(),
            wheel: 0
        };

        // 触摸状态
        this.touchState = {
            touches: new Map(),
            gestures: {
                pinch: { active: false, scale: 1.0 },
                pan: { active: false, delta: Vector2.Zero() }
            }
        };

        // 手柄状态
        this.gamepadState = new Map();

        // 移动输入状态
        this.movementInput = Vector3.Zero();
        this.isMoving = false;

        // 相机输入状态
        this.cameraInput = {
            rotation: Vector2.Zero(),
            zoom: 0
        };
    }

    /**
     * 重置所有输入状态
     */
    reset() {
        this.keyboardState.clear();
        this.mouseState.buttons.clear();
        this.mouseState.deltaPosition.setAll(0);
        this.mouseState.wheel = 0;
        this.touchState.touches.clear();
        this.touchState.gestures.pinch.active = false;
        this.touchState.gestures.pan.active = false;
        this.gamepadState.clear();
        this.movementInput.setAll(0);
        this.isMoving = false;
        this.cameraInput.rotation.setAll(0);
        this.cameraInput.zoom = 0;
    }

    /**
     * 获取键盘按键状态
     * @param {string} key - 按键名称
     * @returns {boolean} 是否按下
     */
    isKeyPressed(key) {
        return this.keyboardState.get(key.toLowerCase()) || false;
    }

    /**
     * 设置键盘按键状态
     * @param {string} key - 按键名称
     * @param {boolean} pressed - 是否按下
     */
    setKeyState(key, pressed) {
        this.keyboardState.set(key.toLowerCase(), pressed);
    }

    /**
     * 获取鼠标按钮状态
     * @param {number} button - 按钮索引
     * @returns {boolean} 是否按下
     */
    isMouseButtonPressed(button) {
        return this.mouseState.buttons.get(button) || false;
    }

    /**
     * 设置鼠标按钮状态
     * @param {number} button - 按钮索引
     * @param {boolean} pressed - 是否按下
     */
    setMouseButtonState(button, pressed) {
        this.mouseState.buttons.set(button, pressed);
    }

    /**
     * 更新鼠标位置
     * @param {Vector2} position - 新位置
     */
    updateMousePosition(position) {
        this.mouseState.deltaPosition = position.subtract(this.mouseState.position);
        this.mouseState.position = position.clone();
    }

    /**
     * 更新移动输入
     * @param {Vector3} movement - 移动向量
     */
    updateMovementInput(movement) {
        this.movementInput.copyFrom(movement);
        this.isMoving = movement.lengthSquared() > 0.001;
    }

    /**
     * 更新相机输入
     * @param {Vector2} rotation - 旋转增量
     * @param {number} zoom - 缩放增量
     */
    updateCameraInput(rotation, zoom = 0) {
        this.cameraInput.rotation.copyFrom(rotation);
        this.cameraInput.zoom = zoom;
    }
}

/**
 * 核心输入管理器类
 * 统一管理所有输入设备和事件
 */
export class InputManager extends EventEmitter {
    constructor(scene, config = {}) {
        super();

        this.scene = scene;
        this.config = config;

        // 输入状态管理器
        this.state = new InputStateManager();

        // 设备适配器
        this.devices = new Map();

        // 输入映射配置
        this.inputMappings = new Map();

        // 是否已初始化
        this.initialized = false;

        // 是否启用
        this.enabled = true;

        // 调试模式
        this.debugMode = config.debug || false;

        console.log('🎮 输入管理器已创建');
    }

    /**
     * 初始化输入管理器
     * 设置所有输入设备的监听器
     */
    async initialize() {
        if (this.initialized) {
            console.warn('输入管理器已经初始化');
            return;
        }

        try {
            // 初始化键盘输入
            this.initializeKeyboard();

            // 初始化鼠标输入
            this.initializeMouse();

            // 初始化触摸输入
            this.initializeTouch();

            // 初始化手柄输入
            await this.initializeGamepad();

            // 设置默认输入映射
            this.setupDefaultMappings();

            this.initialized = true;

            console.log('✅ 输入管理器初始化完成');
            this.emit('initialized');

        } catch (error) {
            console.error('❌ 输入管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化键盘输入
     */
    initializeKeyboard() {
        if (!this.scene) return;

        this.scene.onKeyboardObservable.add((keyboardInfo) => {
            if (!this.enabled) return;

            const key = keyboardInfo.event.key.toLowerCase();
            const isPressed = keyboardInfo.type === KeyboardEventTypes.KEYDOWN;

            // 更新状态
            this.state.setKeyState(key, isPressed);

            // 处理输入映射
            this.processKeyboardInput(key, isPressed, keyboardInfo.event);

            if (this.debugMode) {
                console.log(`键盘输入: ${key} ${isPressed ? '按下' : '释放'}`);
            }
        });

        console.log('🎹 键盘输入已初始化');
    }

    /**
     * 初始化鼠标输入
     */
    initializeMouse() {
        if (!this.scene) return;

        this.scene.onPointerObservable.add((pointerInfo) => {
            if (!this.enabled) return;

            const event = pointerInfo.event;
            const position = new Vector2(event.clientX, event.clientY);

            switch (pointerInfo.type) {
                case PointerEventTypes.POINTERDOWN:
                    this.state.setMouseButtonState(event.button, true);
                    this.processMouseInput('down', event.button, position, event);
                    break;

                case PointerEventTypes.POINTERUP:
                    this.state.setMouseButtonState(event.button, false);
                    this.processMouseInput('up', event.button, position, event);
                    break;

                case PointerEventTypes.POINTERMOVE:
                    this.state.updateMousePosition(position);
                    this.processMouseInput('move', -1, position, event);
                    break;

                case PointerEventTypes.POINTERWHEEL:
                    const wheelDelta = event.deltaY;
                    this.state.mouseState.wheel = wheelDelta;
                    this.processMouseInput('wheel', -1, position, event);
                    break;
            }

            if (this.debugMode && pointerInfo.type !== PointerEventTypes.POINTERMOVE) {
                console.log(`鼠标输入: ${pointerInfo.type} 按钮:${event.button} 位置:(${position.x}, ${position.y})`);
            }
        });

        console.log('🖱️ 鼠标输入已初始化');
    }

    /**
     * 初始化触摸输入
     */
    initializeTouch() {
        // 触摸输入将在后续版本中实现
        console.log('📱 触摸输入已准备 (待实现)');
    }

    /**
     * 初始化手柄输入
     */
    async initializeGamepad() {
        // 手柄输入将在后续版本中实现
        console.log('🎮 手柄输入已准备 (待实现)');
    }

    /**
     * 设置默认输入映射
     */
    setupDefaultMappings() {
        // 移动控制
        this.setInputMapping('w', InputEventTypes.MOVEMENT_UPDATE, { direction: 'forward' });
        this.setInputMapping('s', InputEventTypes.MOVEMENT_UPDATE, { direction: 'backward' });
        this.setInputMapping('a', InputEventTypes.MOVEMENT_UPDATE, { direction: 'left' });
        this.setInputMapping('d', InputEventTypes.MOVEMENT_UPDATE, { direction: 'right' });
        this.setInputMapping('arrowup', InputEventTypes.MOVEMENT_UPDATE, { direction: 'forward' });
        this.setInputMapping('arrowdown', InputEventTypes.MOVEMENT_UPDATE, { direction: 'backward' });
        this.setInputMapping('arrowleft', InputEventTypes.MOVEMENT_UPDATE, { direction: 'left' });
        this.setInputMapping('arrowright', InputEventTypes.MOVEMENT_UPDATE, { direction: 'right' });

        // 动作控制
        this.setInputMapping(' ', InputEventTypes.ACTION_JUMP);
        this.setInputMapping('shift', InputEventTypes.ACTION_RUN);
        this.setInputMapping('e', InputEventTypes.ACTION_INTERACT);
        this.setInputMapping('f', InputEventTypes.ACTION_ATTACK);

        // UI控制
        this.setInputMapping('escape', InputEventTypes.UI_MENU);
        this.setInputMapping('i', InputEventTypes.UI_INVENTORY);
        this.setInputMapping('enter', InputEventTypes.UI_CHAT);
        this.setInputMapping('m', InputEventTypes.UI_MAP);

        // 技能快捷键
        this.setInputMapping('1', InputEventTypes.SKILL_SLOT_1);
        this.setInputMapping('2', InputEventTypes.SKILL_SLOT_2);
        this.setInputMapping('3', InputEventTypes.SKILL_SLOT_3);
        this.setInputMapping('4', InputEventTypes.SKILL_SLOT_4);
        this.setInputMapping('5', InputEventTypes.SKILL_SLOT_5);
        this.setInputMapping('6', InputEventTypes.SKILL_SLOT_6);
        this.setInputMapping('7', InputEventTypes.SKILL_SLOT_7);
        this.setInputMapping('8', InputEventTypes.SKILL_SLOT_8);
        this.setInputMapping('9', InputEventTypes.SKILL_SLOT_9);
        this.setInputMapping('0', InputEventTypes.SKILL_SLOT_0);

        // 系统控制
        this.setInputMapping('p', InputEventTypes.SYSTEM_PAUSE);
        this.setInputMapping('f12', InputEventTypes.SYSTEM_SCREENSHOT);

        // 鼠标映射
        this.setInputMapping('mouse:move', InputEventTypes.CAMERA_ROTATE);
        this.setInputMapping('mouse:wheel', InputEventTypes.CAMERA_ZOOM);
        this.setInputMapping('mouse:0', InputEventTypes.ACTION_ATTACK); // 左键攻击
        this.setInputMapping('mouse:2', InputEventTypes.ACTION_INTERACT); // 右键交互

        console.log('⚙️ 默认输入映射已设置');
    }

    /**
     * 设置输入映射
     * @param {string} input - 输入标识符 (如 'w', 'mouse:0', 'gamepad:a')
     * @param {string} eventType - 事件类型
     * @param {Object} data - 附加数据
     */
    setInputMapping(input, eventType, data = {}) {
        if (!this.inputMappings.has(input)) {
            this.inputMappings.set(input, []);
        }
        this.inputMappings.get(input).push({ eventType, data });
    }

    /**
     * 移除输入映射
     * @param {string} input - 输入标识符
     * @param {string} eventType - 事件类型 (可选，不指定则移除所有)
     */
    removeInputMapping(input, eventType = null) {
        if (!this.inputMappings.has(input)) return;

        if (eventType) {
            const mappings = this.inputMappings.get(input);
            const filtered = mappings.filter(mapping => mapping.eventType !== eventType);
            this.inputMappings.set(input, filtered);
        } else {
            this.inputMappings.delete(input);
        }
    }

    /**
     * 处理键盘输入
     * @param {string} key - 按键
     * @param {boolean} isPressed - 是否按下
     * @param {KeyboardEvent} event - 原始事件
     */
    processKeyboardInput(key, isPressed, event) {
        const mappings = this.inputMappings.get(key);
        if (!mappings) return;

        for (const mapping of mappings) {
            const eventData = {
                device: InputDeviceTypes.KEYBOARD,
                key: key,
                pressed: isPressed,
                originalEvent: event,
                ...mapping.data
            };

            // 特殊处理移动输入
            if (mapping.eventType === InputEventTypes.MOVEMENT_UPDATE) {
                this.updateMovementFromKeyboard();
            } else {
                // 只在按下时触发非移动事件
                if (isPressed) {
                    this.emit(mapping.eventType, eventData);
                }
            }
        }
    }

    /**
     * 处理鼠标输入
     * @param {string} action - 动作类型 ('down', 'up', 'move', 'wheel')
     * @param {number} button - 按钮索引
     * @param {Vector2} position - 位置
     * @param {PointerEvent} event - 原始事件
     */
    processMouseInput(action, button, position, event) {
        let inputKey = '';

        switch (action) {
            case 'down':
            case 'up':
                inputKey = `mouse:${button}`;
                break;
            case 'move':
                inputKey = 'mouse:move';
                break;
            case 'wheel':
                inputKey = 'mouse:wheel';
                break;
        }

        const mappings = this.inputMappings.get(inputKey);
        if (!mappings) return;

        for (const mapping of mappings) {
            const eventData = {
                device: InputDeviceTypes.MOUSE,
                action: action,
                button: button,
                position: position,
                delta: this.state.mouseState.deltaPosition,
                wheel: this.state.mouseState.wheel,
                originalEvent: event,
                ...mapping.data
            };

            // 特殊处理相机输入
            if (mapping.eventType === InputEventTypes.CAMERA_ROTATE && action === 'move') {
                this.updateCameraFromMouse();
            } else if (mapping.eventType === InputEventTypes.CAMERA_ZOOM && action === 'wheel') {
                this.updateCameraFromMouse();
            } else {
                // 只在按下时触发按钮事件
                if (action === 'down') {
                    this.emit(mapping.eventType, eventData);
                }
            }
        }
    }

    /**
     * 从键盘输入更新移动状态
     */
    updateMovementFromKeyboard() {
        const movement = Vector3.Zero();

        // 检查各方向键状态
        if (this.state.isKeyPressed('w') || this.state.isKeyPressed('arrowup')) {
            movement.z += 1;
        }
        if (this.state.isKeyPressed('s') || this.state.isKeyPressed('arrowdown')) {
            movement.z -= 1;
        }
        if (this.state.isKeyPressed('a') || this.state.isKeyPressed('arrowleft')) {
            movement.x -= 1;
        }
        if (this.state.isKeyPressed('d') || this.state.isKeyPressed('arrowright')) {
            movement.x += 1;
        }

        // 标准化移动向量
        if (movement.lengthSquared() > 0.001) {
            movement.normalize();
        }

        // 更新状态
        const wasMoving = this.state.isMoving;
        this.state.updateMovementInput(movement);

        // 发送移动事件
        if (!wasMoving && this.state.isMoving) {
            this.emit(InputEventTypes.MOVEMENT_START, {
                device: InputDeviceTypes.KEYBOARD,
                movement: movement.clone()
            });
        } else if (wasMoving && !this.state.isMoving) {
            this.emit(InputEventTypes.MOVEMENT_STOP, {
                device: InputDeviceTypes.KEYBOARD,
                movement: Vector3.Zero()
            });
        } else if (this.state.isMoving) {
            this.emit(InputEventTypes.MOVEMENT_UPDATE, {
                device: InputDeviceTypes.KEYBOARD,
                movement: movement.clone()
            });
        }
    }

    /**
     * 从鼠标输入更新相机状态
     */
    updateCameraFromMouse() {
        const rotation = this.state.mouseState.deltaPosition.clone();
        const zoom = this.state.mouseState.wheel;

        this.state.updateCameraInput(rotation, zoom);

        // 发送相机事件
        if (rotation.lengthSquared() > 0.001) {
            this.emit(InputEventTypes.CAMERA_ROTATE, {
                device: InputDeviceTypes.MOUSE,
                rotation: rotation,
                sensitivity: this.config.mouseSensitivity || 1.0
            });
        }

        if (Math.abs(zoom) > 0.001) {
            this.emit(InputEventTypes.CAMERA_ZOOM, {
                device: InputDeviceTypes.MOUSE,
                zoom: zoom,
                sensitivity: this.config.zoomSensitivity || 1.0
            });
        }
    }

    /**
     * 每帧更新输入管理器
     * 应该在游戏主循环中调用
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (!this.enabled || !this.initialized) return;

        // 更新手柄状态 (如果有)
        this.updateGamepads();

        // 清理一次性状态
        this.state.mouseState.deltaPosition.setAll(0);
        this.state.mouseState.wheel = 0;
    }

    /**
     * 更新手柄状态
     */
    updateGamepads() {
        // 手柄更新逻辑将在后续版本中实现
    }

    /**
     * 启用输入管理器
     */
    enable() {
        this.enabled = true;
        console.log('🎮 输入管理器已启用');
    }

    /**
     * 禁用输入管理器
     */
    disable() {
        this.enabled = false;
        this.state.reset();
        console.log('🎮 输入管理器已禁用');
    }

    /**
     * 获取当前移动输入
     * @returns {Vector3} 移动向量
     */
    getMovementInput() {
        return this.state.movementInput.clone();
    }

    /**
     * 获取当前相机输入
     * @returns {Object} 相机输入状态
     */
    getCameraInput() {
        return {
            rotation: this.state.cameraInput.rotation.clone(),
            zoom: this.state.cameraInput.zoom
        };
    }

    /**
     * 检查按键是否按下
     * @param {string} key - 按键名称
     * @returns {boolean} 是否按下
     */
    isKeyPressed(key) {
        return this.state.isKeyPressed(key);
    }

    /**
     * 检查鼠标按钮是否按下
     * @param {number} button - 按钮索引
     * @returns {boolean} 是否按下
     */
    isMouseButtonPressed(button) {
        return this.state.isMouseButtonPressed(button);
    }

    /**
     * 获取鼠标位置
     * @returns {Vector2} 鼠标位置
     */
    getMousePosition() {
        return this.state.mouseState.position.clone();
    }

    /**
     * 获取鼠标移动增量
     * @returns {Vector2} 移动增量
     */
    getMouseDelta() {
        return this.state.mouseState.deltaPosition.clone();
    }

    /**
     * 设置调试模式
     * @param {boolean} enabled - 是否启用调试
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`🐛 输入管理器调试模式: ${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 获取输入映射配置
     * @returns {Map} 输入映射
     */
    getInputMappings() {
        return new Map(this.inputMappings);
    }

    /**
     * 清除所有输入映射
     */
    clearInputMappings() {
        this.inputMappings.clear();
        console.log('🗑️ 所有输入映射已清除');
    }

    /**
     * 重置为默认映射
     */
    resetToDefaultMappings() {
        this.clearInputMappings();
        this.setupDefaultMappings();
        console.log('🔄 输入映射已重置为默认');
    }

    /**
     * 销毁输入管理器
     * 清理所有监听器和状态
     */
    destroy() {
        this.disable();
        this.clearInputMappings();
        this.removeAllListeners();
        this.initialized = false;

        console.log('💥 输入管理器已销毁');
    }

    /**
     * 获取输入统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            initialized: this.initialized,
            enabled: this.enabled,
            debugMode: this.debugMode,
            mappingCount: this.inputMappings.size,
            keyboardKeysPressed: Array.from(this.state.keyboardState.entries())
                .filter(([key, pressed]) => pressed)
                .map(([key]) => key),
            mouseButtonsPressed: Array.from(this.state.mouseState.buttons.entries())
                .filter(([button, pressed]) => pressed)
                .map(([button]) => button),
            isMoving: this.state.isMoving,
            movementInput: this.state.movementInput.clone(),
            mousePosition: this.state.mouseState.position.clone()
        };
    }
}

// 创建全局输入管理器实例
let globalInputManager = null;

/**
 * 获取全局输入管理器实例
 * @returns {InputManager} 输入管理器实例
 */
export function getGlobalInputManager() {
    return globalInputManager;
}

/**
 * 初始化全局输入管理器
 * @param {Scene} scene - Babylon.js场景
 * @param {Object} config - 配置选项
 * @returns {InputManager} 输入管理器实例
 */
export async function initializeGlobalInputManager(scene, config = {}) {
    if (globalInputManager) {
        console.warn('全局输入管理器已存在，将销毁旧实例');
        globalInputManager.destroy();
    }

    globalInputManager = new InputManager(scene, config);
    await globalInputManager.initialize();

    // 暴露到全局作用域以便调试
    if (typeof window !== 'undefined') {
        window.inputManager = globalInputManager;
    }

    console.log('🌍 全局输入管理器已初始化');
    return globalInputManager;
}

/**
 * 销毁全局输入管理器
 */
export function destroyGlobalInputManager() {
    if (globalInputManager) {
        globalInputManager.destroy();
        globalInputManager = null;

        if (typeof window !== 'undefined') {
            delete window.inputManager;
        }

        console.log('🌍 全局输入管理器已销毁');
    }
}

console.log('🎮 输入管理器模块已加载');
